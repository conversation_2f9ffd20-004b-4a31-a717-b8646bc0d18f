"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Zap, 
  Shield, 
  MessageCircle,
  Clock,
  Star
} from "lucide-react"
import { ProductPackage, Currency } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"

interface ModernCartControlsProps {
  selectedPackage: ProductPackage | null
  quantity: number
  currency: Currency
  onQuantityChange: (change: number) => void
  onPurchase: () => void
  onAddToCart?: () => void
  isLoading?: boolean
  disabled?: boolean
  processingType?: "instant" | "manual"
  estimatedTime?: string
  showQuantity?: boolean
}

export function ModernCartControls({
  selectedPackage,
  quantity,
  currency,
  onQuantityChange,
  onPurchase,
  onAddToCart,
  isLoading = false,
  disabled = false,
  processingType = "instant",
  estimatedTime = "فوري",
  showQuantity = true
}: ModernCartControlsProps) {
  const { convertPrice } = useCurrencyConverter()

  if (!selectedPackage) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-2">اختر حزمة للمتابعة</div>
        <div className="text-slate-500 text-sm">👆 اختر من الحزم أعلاه</div>
      </div>
    )
  }

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = convertPrice(priceUSD, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  const totalPrice = selectedPackage.price * quantity
  const originalTotal = selectedPackage.originalPrice 
    ? selectedPackage.originalPrice * quantity 
    : totalPrice
  const savings = originalTotal - totalPrice

  return (
    <div className="space-y-4" dir="rtl">
      {/* Quantity Selector */}
      {showQuantity && (
        <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="text-white font-medium">الكمية</div>
                <div className="text-slate-400 text-sm">
                  {selectedPackage.name} × {quantity}
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onQuantityChange(-1)}
                  disabled={quantity <= 1 || disabled}
                  className="h-10 w-10 p-0 border-slate-600 hover:bg-slate-700 hover:border-slate-500"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                
                <div className="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 min-w-[60px] text-center">
                  <span className="text-white font-bold text-lg">{quantity}</span>
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onQuantityChange(1)}
                  disabled={disabled}
                  className="h-10 w-10 p-0 border-slate-600 hover:bg-slate-700 hover:border-slate-500"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Price Summary */}
      <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
        <CardContent className="p-4 space-y-3">
          {/* Package Info */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="text-white font-medium">{selectedPackage.name}</div>
              <div className="text-slate-400 text-sm">{selectedPackage.amount}</div>
            </div>
            {selectedPackage.popular && (
              <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
                <Star className="h-3 w-3 ml-1 fill-current" />
                الأشهر
              </Badge>
            )}
          </div>

          {/* Pricing Breakdown */}
          <div className="space-y-2 pt-2 border-t border-slate-700">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-300">سعر الوحدة:</span>
              <span className="text-white">{formatPrice(selectedPackage.price)}</span>
            </div>
            
            {showQuantity && quantity > 1 && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-300">الكمية:</span>
                <span className="text-white">×{quantity}</span>
              </div>
            )}
            
            {savings > 0 && (
              <>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-400">السعر الأصلي:</span>
                  <span className="text-slate-500 line-through">
                    {formatPrice(originalTotal)}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-green-400">الخصم:</span>
                  <span className="text-green-400">
                    -{formatPrice(savings)}
                  </span>
                </div>
              </>
            )}
          </div>

          {/* Total */}
          <div className="flex items-center justify-between text-lg font-bold pt-2 border-t border-slate-700">
            <span className="text-white">المجموع:</span>
            <span className="text-yellow-400">{formatPrice(totalPrice)}</span>
          </div>

          {/* Savings Badge */}
          {savings > 0 && (
            <div className="text-center">
              <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                وفرت {formatPrice(savings)} 🎉
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="space-y-3">
        {/* Primary Purchase Button */}
        <Button
          onClick={onPurchase}
          disabled={disabled || isLoading}
          className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold h-14 text-lg shadow-lg hover:shadow-xl transition-all duration-200"
          size="lg"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 border-2 border-slate-900/30 border-t-slate-900 rounded-full animate-spin"></div>
              جاري المعالجة...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              اشتري الآن - {formatPrice(totalPrice)}
            </div>
          )}
        </Button>

        {/* Secondary Actions */}
        <div className="flex gap-3">
          {onAddToCart && (
            <Button
              onClick={onAddToCart}
              variant="outline"
              disabled={disabled || isLoading}
              className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500 h-12"
            >
              أضف للسلة
            </Button>
          )}
          
          <Button
            variant="outline"
            size="lg"
            disabled={disabled}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500 h-12 px-4"
          >
            <MessageCircle className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Trust Indicators */}
      <div className="grid grid-cols-3 gap-4 pt-4">
        <div className="text-center space-y-1">
          <div className="flex justify-center">
            {processingType === "instant" ? (
              <Zap className="h-5 w-5 text-green-400" />
            ) : (
              <Clock className="h-5 w-5 text-blue-400" />
            )}
          </div>
          <div className="text-xs text-slate-400">
            {processingType === "instant" ? "تسليم فوري" : estimatedTime}
          </div>
        </div>
        
        <div className="text-center space-y-1">
          <div className="flex justify-center">
            <Shield className="h-5 w-5 text-blue-400" />
          </div>
          <div className="text-xs text-slate-400">دفع آمن</div>
        </div>
        
        <div className="text-center space-y-1">
          <div className="flex justify-center">
            <MessageCircle className="h-5 w-5 text-purple-400" />
          </div>
          <div className="text-xs text-slate-400">دعم 24/7</div>
        </div>
      </div>

      {/* Currency Note */}
      {currency !== "USD" && (
        <div className="text-center">
          <p className="text-xs text-slate-500">
            الأسعار محولة من الدولار الأمريكي • قد تختلف أسعار الصرف
          </p>
        </div>
      )}
    </div>
  )
}

/**
 * Sticky bottom version for mobile
 */
export function StickyCartControls({
  selectedPackage,
  quantity,
  currency,
  onPurchase,
  isLoading = false,
  disabled = false,
  processingType = "instant"
}: Pick<ModernCartControlsProps, 'selectedPackage' | 'quantity' | 'currency' | 'onPurchase' | 'isLoading' | 'disabled' | 'processingType'>) {
  const { convertPrice } = useCurrencyConverter()

  if (!selectedPackage) return null

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = convertPrice(priceUSD, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  const totalPrice = selectedPackage.price * quantity

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-sm border-t border-slate-700 p-4 z-50" dir="rtl">
      <div className="flex gap-3 items-center">
        <Button
          onClick={onPurchase}
          disabled={disabled || isLoading}
          className="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-slate-900 font-bold h-12 text-lg"
        >
          {isLoading ? (
            "جاري المعالجة..."
          ) : (
            <>
              <ShoppingCart className="h-5 w-5 ml-2" />
              اشتري الآن - {formatPrice(totalPrice)}
            </>
          )}
        </Button>
        
        <Button
          variant="outline"
          size="lg"
          className="border-slate-600 text-slate-300 hover:bg-slate-700 h-12 px-4"
        >
          <MessageCircle className="h-5 w-5" />
        </Button>
      </div>
      
      <div className="flex items-center justify-center gap-4 mt-3 text-xs text-slate-400">
        <div className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          <span>دفع آمن</span>
        </div>
        <div className="flex items-center gap-1">
          {processingType === "instant" ? (
            <Zap className="h-3 w-3" />
          ) : (
            <Clock className="h-3 w-3" />
          )}
          <span>{processingType === "instant" ? "تسليم فوري" : "معالجة سريعة"}</span>
        </div>
      </div>
    </div>
  )
}
