"use client"

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { AppStore, BulkAppData, BulkDataResponse } from '@/lib/types'

// Initial state
const initialState = {
  data: null,
  isLoading: false,
  isInitialized: false,
  error: null,
  lastUpdated: null,
  isOffline: false,
}

// Create the store with persistence
export const useAppStore = create<AppStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Load all application data in a single request
      loadBulkData: async () => {
        const state = get()

        // Don't reload if already loading or recently loaded (within 5 minutes)
        if (state.isLoading) return

        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
        if (state.lastUpdated && new Date(state.lastUpdated) > fiveMinutesAgo) {
          return
        }

        set({ isLoading: true, error: null })

        try {
          const response = await fetch('/api/bulk-data', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          const result: BulkDataResponse = await response.json()

          if (!result.success) {
            throw new Error(result.error || 'Failed to load application data')
          }

          set({
            data: result.data,
            isLoading: false,
            isInitialized: true,
            error: null,
            lastUpdated: new Date(),
            isOffline: false,
          })

        } catch (error) {
          console.error('Failed to load bulk data:', error)

          // Check if we have cached data to fall back to
          const currentData = get().data
          if (currentData) {
            // We have cached data, just update loading state and set offline mode
            set({
              isLoading: false,
              error: 'Using cached data - offline mode',
              isOffline: true,
            })
          } else {
            // No cached data available
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to load data',
              isOffline: true,
            })
          }
        }
      },

      // Update specific parts of the data
      updateData: (newData: Partial<BulkAppData>) => {
        const currentData = get().data
        if (currentData) {
          set({
            data: { ...currentData, ...newData },
            lastUpdated: new Date(),
          })
        }
      },

      // Set error state
      setError: (error: string | null) => {
        set({ error })
      },

      // Clear all data (useful for logout)
      clearData: () => {
        set(initialState)
      },

      // Force refresh data
      refreshData: async () => {
        set({ lastUpdated: null }) // Reset timestamp to force reload
        await get().loadBulkData()
      },

      // Set offline mode
      setOfflineMode: (isOffline: boolean) => {
        set({ isOffline })
      },
    }),
    {
      name: 'alraya-app-data', // localStorage key
      storage: createJSONStorage(() => localStorage),
      // Only persist the data, not loading states
      partialize: (state) => ({
        data: state.data,
        lastUpdated: state.lastUpdated,
        isInitialized: state.isInitialized,
      }),
      // Rehydrate loading states on startup
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.isLoading = false
          state.error = null
          state.isOffline = false
        }
      },
    }
  )
)

// Selector hooks for specific data
export const useProducts = () => useAppStore((state) => state.data?.products || [])
export const useCurrencies = () => useAppStore((state) => state.data?.currencies || [])
export const useExchangeRates = () => useAppStore((state) => state.data?.exchangeRates || {})
export const useGameCards = () => useAppStore((state) => state.data?.gameCards || [])
export const useSlides = () => useAppStore((state) => state.data?.slides || [])
export const useWalletData = () => useAppStore((state) => state.data?.walletData)
export const useUserPreferences = () => useAppStore((state) => state.data?.userPreferences)

// Loading state selectors
export const useAppLoading = () => useAppStore((state) => state.isLoading)
export const useAppError = () => useAppStore((state) => state.error)
export const useAppInitialized = () => useAppStore((state) => state.isInitialized)
export const useAppOffline = () => useAppStore((state) => state.isOffline)

// Action selectors - individual hooks to prevent object recreation
export const useLoadBulkData = () => useAppStore((state) => state.loadBulkData)
export const useUpdateData = () => useAppStore((state) => state.updateData)
export const useSetError = () => useAppStore((state) => state.setError)
export const useClearData = () => useAppStore((state) => state.clearData)
export const useRefreshData = () => useAppStore((state) => state.refreshData)
export const useSetOfflineMode = () => useAppStore((state) => state.setOfflineMode)

// Legacy combined actions hook (deprecated - use individual hooks above)
export const useAppActions = () => useAppStore((state) => ({
  loadBulkData: state.loadBulkData,
  updateData: state.updateData,
  setError: state.setError,
  clearData: state.clearData,
  refreshData: state.refreshData,
  setOfflineMode: state.setOfflineMode,
}))
