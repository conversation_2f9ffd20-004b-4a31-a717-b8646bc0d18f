import { ProductCategory } from "@/lib/types"

const STORAGE_KEY = "product_categories"

/**
 * Simple category management service using localStorage
 * MINIMAL: Basic CRUD operations for gaming product categories
 */

export function getCategories(): ProductCategory[] {
  try {
    const stored = localStorage.getItem(STORAGE_KEY)
    if (!stored) return getDefaultCategories()
    
    const categories = JSON.parse(stored)
    return categories.map((cat: any) => ({
      ...cat,
      createdAt: new Date(cat.createdAt),
      updatedAt: new Date(cat.updatedAt)
    }))
  } catch (error) {
    console.error("Error loading categories:", error)
    return getDefaultCategories()
  }
}

export function saveCategories(categories: ProductCategory[]): void {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(categories))
  } catch (error) {
    console.error("Error saving categories:", error)
    throw new Error("فشل في حفظ الفئات")
  }
}

export function createCategory(name: string, image: string): ProductCategory {
  const categories = getCategories()

  // Check for duplicate names
  if (categories.some(cat => cat.name.toLowerCase() === name.toLowerCase())) {
    throw new Error("اسم الفئة موجود بالفعل")
  }

  if (!image.trim()) {
    throw new Error("صورة الفئة مطلوبة")
  }

  const newCategory: ProductCategory = {
    id: generateId(),
    name: name.trim(),
    image: image.trim(),
    createdAt: new Date(),
    updatedAt: new Date()
  }

  const updatedCategories = [...categories, newCategory]
  saveCategories(updatedCategories)

  return newCategory
}

export function updateCategory(id: string, updates: Partial<Pick<ProductCategory, 'name' | 'image'>>): ProductCategory {
  const categories = getCategories()
  const index = categories.findIndex(cat => cat.id === id)

  if (index === -1) {
    throw new Error("الفئة غير موجودة")
  }

  // Check for duplicate names (excluding current category)
  if (updates.name) {
    const nameExists = categories.some(cat =>
      cat.id !== id && cat.name.toLowerCase() === updates.name!.toLowerCase()
    )
    if (nameExists) {
      throw new Error("اسم الفئة موجود بالفعل")
    }
  }

  // Validate image if provided
  if (updates.image !== undefined && !updates.image.trim()) {
    throw new Error("صورة الفئة مطلوبة")
  }

  const updatedCategory = {
    ...categories[index],
    ...updates,
    updatedAt: new Date()
  }

  categories[index] = updatedCategory
  saveCategories(categories)

  return updatedCategory
}

export function deleteCategory(id: string): void {
  const categories = getCategories()
  const filteredCategories = categories.filter(cat => cat.id !== id)
  
  if (filteredCategories.length === categories.length) {
    throw new Error("الفئة غير موجودة")
  }
  
  saveCategories(filteredCategories)
}

export function getCategoryById(id: string): ProductCategory | null {
  const categories = getCategories()
  return categories.find(cat => cat.id === id) || null
}

export function getCategoryByName(name: string): ProductCategory | null {
  const categories = getCategories()
  return categories.find(cat => cat.name.toLowerCase() === name.toLowerCase()) || null
}

// Helper function to generate unique IDs
function generateId(): string {
  return `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Default gaming categories with placeholder images
function getDefaultCategories(): ProductCategory[] {
  const now = new Date()
  return [
    {
      id: "cat_pubg_mobile",
      name: "PUBG Mobile",
      image: "https://via.placeholder.com/100x100/1e293b/f1f5f9?text=PUBG",
      createdAt: now,
      updatedAt: now
    },
    {
      id: "cat_free_fire",
      name: "Free Fire",
      image: "https://via.placeholder.com/100x100/dc2626/ffffff?text=FF",
      createdAt: now,
      updatedAt: now
    },
    {
      id: "cat_gift_cards",
      name: "بطاقات الهدايا",
      image: "https://via.placeholder.com/100x100/059669/ffffff?text=GIFT",
      createdAt: now,
      updatedAt: now
    },
    {
      id: "cat_other_games",
      name: "ألعاب أخرى",
      image: "https://via.placeholder.com/100x100/7c3aed/ffffff?text=GAMES",
      createdAt: now,
      updatedAt: now
    }
  ]
}

// Initialize default categories if none exist
export function initializeCategories(): void {
  const existing = localStorage.getItem(STORAGE_KEY)
  if (!existing) {
    saveCategories(getDefaultCategories())
  }
}
