"use client"

import React, { useState, useEffect } from "react"
import { Check, X, Alert<PERSON>riangle, Info } from "lucide-react"

interface NotificationProps {
  type: "success" | "error" | "warning" | "info"
  title: string
  message?: string
  duration?: number
  onClose?: () => void
}

export function Notification({ 
  type, 
  title, 
  message, 
  duration = 4000, 
  onClose 
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => onClose?.(), 300) // Wait for animation
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [duration, onClose])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose?.(), 300)
  }

  const getIcon = () => {
    switch (type) {
      case "success": return <Check className="h-5 w-5" />
      case "error": return <X className="h-5 w-5" />
      case "warning": return <AlertTriangle className="h-5 w-5" />
      case "info": return <Info className="h-5 w-5" />
    }
  }

  const getColors = () => {
    switch (type) {
      case "success": return "bg-green-500/20 border-green-500/30 text-green-400"
      case "error": return "bg-red-500/20 border-red-500/30 text-red-400"
      case "warning": return "bg-yellow-500/20 border-yellow-500/30 text-yellow-400"
      case "info": return "bg-blue-500/20 border-blue-500/30 text-blue-400"
    }
  }

  if (!isVisible) return null

  return (
    <div className={`
      fixed top-4 right-4 z-50 max-w-sm w-full
      transform transition-all duration-300 ease-in-out
      ${isVisible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
    `}>
      <div className={`
        rounded-lg border p-4 shadow-lg backdrop-blur-sm
        ${getColors()}
      `}>
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm">{title}</h4>
            {message && (
              <p className="text-xs opacity-90 mt-1">{message}</p>
            )}
          </div>
          <button
            onClick={handleClose}
            className="flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}

// Notification Manager
interface NotificationData {
  id: string
  type: "success" | "error" | "warning" | "info"
  title: string
  message?: string
  duration?: number
}

let notificationId = 0
const notificationListeners: ((notifications: NotificationData[]) => void)[] = []
let notifications: NotificationData[] = []

export const showNotification = (
  type: "success" | "error" | "warning" | "info",
  title: string,
  message?: string,
  duration?: number
) => {
  const id = `notification-${++notificationId}`
  const notification: NotificationData = {
    id,
    type,
    title,
    message,
    duration
  }

  notifications = [...notifications, notification]
  notificationListeners.forEach(listener => listener(notifications))

  // Auto remove after duration
  if (duration !== 0) {
    setTimeout(() => {
      removeNotification(id)
    }, duration || 4000)
  }

  return id
}

export const removeNotification = (id: string) => {
  notifications = notifications.filter(n => n.id !== id)
  notificationListeners.forEach(listener => listener(notifications))
}

export function NotificationContainer() {
  const [notificationList, setNotificationList] = useState<NotificationData[]>([])

  useEffect(() => {
    const listener = (newNotifications: NotificationData[]) => {
      setNotificationList(newNotifications)
    }

    notificationListeners.push(listener)

    return () => {
      const index = notificationListeners.indexOf(listener)
      if (index > -1) {
        notificationListeners.splice(index, 1)
      }
    }
  }, [])

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notificationList.map((notification) => (
        <Notification
          key={notification.id}
          type={notification.type}
          title={notification.title}
          message={notification.message}
          duration={0} // Managed by the container
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  )
}

// Helper functions for common notifications
export const showSuccess = (title: string, message?: string) => 
  showNotification("success", title, message)

export const showError = (title: string, message?: string) => 
  showNotification("error", title, message)

export const showWarning = (title: string, message?: string) => 
  showNotification("warning", title, message)

export const showInfo = (title: string, message?: string) => 
  showNotification("info", title, message)
