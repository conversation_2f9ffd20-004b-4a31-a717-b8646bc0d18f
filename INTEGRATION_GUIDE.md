# دليل التكامل التقني | Technical Integration Guide

## 🔗 تكامل Supabase | Supabase Integration

### 1. إعداد قاعدة البيانات | Database Setup

```sql
-- تشغيل ملف المخطط
psql -h your-supabase-host -U postgres -d postgres -f migrations/006_product_management_schema.sql
```

### 2. متغيرات البيئة | Environment Variables

```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
```

### 3. تحديث الخدمات | Update Services

#### productService.ts
```typescript
// استبدال localStorage بـ Supabase
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export async function getProducts(filters?: ProductFilters): Promise<ProductTemplate[]> {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      product_packages(*),
      custom_fields(*)
    `)
    .eq('is_active', filters?.isActive ?? true)
    .order('created_at', { ascending: false })
  
  if (error) throw error
  return data.map(transformProductFromDB)
}
```

## 🔐 الأمان | Security Implementation

### Row Level Security (RLS)

```sql
-- تفعيل RLS على جميع الجداول
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_fields ENABLE ROW LEVEL SECURITY;
ALTER TABLE encrypted_codes ENABLE ROW LEVEL SECURITY;

-- سياسات الوصول العام للمنتجات النشطة
CREATE POLICY "Public can view active products" ON products
  FOR SELECT USING (is_active = true);

-- سياسات الإدارة الكاملة للمشرفين
CREATE POLICY "Admins can manage products" ON products
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');
```

### تشفير الأكواد الرقمية | Digital Code Encryption

```typescript
// lib/utils/encryption.ts
import CryptoJS from 'crypto-js'

const SECRET_KEY = process.env.ENCRYPTION_SECRET_KEY!

export function encryptCode(code: string): string {
  return CryptoJS.AES.encrypt(code, SECRET_KEY).toString()
}

export function decryptCode(encryptedCode: string): string {
  const bytes = CryptoJS.AES.decrypt(encryptedCode, SECRET_KEY)
  return bytes.toString(CryptoJS.enc.Utf8)
}
```

## 📱 واجهات البرمجة | API Endpoints

### منتجات | Products API

```typescript
// app/api/products/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getProducts, createProduct } from '@/lib/services/productService'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filters = {
      category: searchParams.get('category') || undefined,
      isActive: searchParams.get('active') === 'true'
    }
    
    const products = await getProducts(filters)
    return NextResponse.json(products)
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const productData = await request.json()
    const product = await createProduct(productData)
    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to create product' }, { status: 500 })
  }
}
```

### محتوى رقمي | Digital Content API

```typescript
// app/api/digital-content/route.ts
export async function POST(request: NextRequest) {
  try {
    const { packageId, codes, codeType } = await request.json()
    
    await addDigitalCodes(packageId, codes, codeType)
    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Failed to add digital codes' }, { status: 500 })
  }
}
```

## 🎨 تخصيص الواجهة | UI Customization

### ألوان المنتجات | Product Colors

```typescript
// lib/utils/productColors.ts
export const getProductTypeColor = (type: ProductType) => {
  switch (type) {
    case 'digital':
      return 'bg-purple-500/20 text-purple-400 border-purple-500/30'
    case 'physical':
      return 'bg-blue-500/20 text-blue-400 border-blue-500/30'
    case 'service':
      return 'bg-green-500/20 text-green-400 border-green-500/30'
    default:
      return 'bg-slate-500/20 text-slate-400 border-slate-500/30'
  }
}
```

### تخصيص الحقول | Custom Fields

```typescript
// components/admin/CustomFieldRenderer.tsx
export function CustomFieldRenderer({ field, value, onChange }: CustomFieldProps) {
  // إضافة أنواع حقول جديدة
  switch (field.type) {
    case 'phone':
      return <PhoneInput value={value} onChange={onChange} />
    case 'date':
      return <DatePicker value={value} onChange={onChange} />
    case 'color':
      return <ColorPicker value={value} onChange={onChange} />
    default:
      return <DefaultFieldRenderer field={field} value={value} onChange={onChange} />
  }
}
```

## 📊 التحليلات | Analytics Integration

### تتبع الأحداث | Event Tracking

```typescript
// lib/analytics/productAnalytics.ts
export function trackProductView(productId: string, productName: string) {
  // Google Analytics
  gtag('event', 'view_item', {
    item_id: productId,
    item_name: productName,
    item_category: 'product'
  })
  
  // Supabase Analytics
  supabase.from('product_analytics').insert({
    event_type: 'view',
    product_id: productId,
    user_id: getCurrentUserId(),
    timestamp: new Date().toISOString()
  })
}

export function trackProductPurchase(orderData: ProductFormData) {
  gtag('event', 'purchase', {
    transaction_id: orderData.orderId,
    value: orderData.totalPrice,
    currency: orderData.currency,
    items: [{
      item_id: orderData.selectedPackage.id,
      item_name: orderData.selectedPackage.name,
      quantity: orderData.quantity,
      price: orderData.selectedPackage.price
    }]
  })
}
```

## 🔄 التزامن | Real-time Synchronization

### اشتراكات Supabase | Supabase Subscriptions

```typescript
// hooks/useRealtimeProducts.ts
export function useRealtimeProducts() {
  const [products, setProducts] = useState<ProductTemplate[]>([])
  
  useEffect(() => {
    const subscription = supabase
      .channel('products')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'products' },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setProducts(prev => [...prev, payload.new as ProductTemplate])
          } else if (payload.eventType === 'UPDATE') {
            setProducts(prev => prev.map(p => 
              p.id === payload.new.id ? payload.new as ProductTemplate : p
            ))
          } else if (payload.eventType === 'DELETE') {
            setProducts(prev => prev.filter(p => p.id !== payload.old.id))
          }
        }
      )
      .subscribe()
    
    return () => {
      subscription.unsubscribe()
    }
  }, [])
  
  return products
}
```

## 🚀 الأداء | Performance Optimization

### تحسين الصور | Image Optimization

```typescript
// lib/utils/imageOptimization.ts
export async function uploadProductImage(file: File): Promise<string> {
  // ضغط الصورة
  const compressedFile = await compressImage(file, {
    maxWidth: 800,
    maxHeight: 600,
    quality: 0.8
  })
  
  // رفع إلى Supabase Storage
  const { data, error } = await supabase.storage
    .from('product-images')
    .upload(`${Date.now()}-${file.name}`, compressedFile)
  
  if (error) throw error
  
  // إرجاع URL العام
  const { data: { publicUrl } } = supabase.storage
    .from('product-images')
    .getPublicUrl(data.path)
  
  return publicUrl
}
```

### تخزين مؤقت | Caching Strategy

```typescript
// lib/cache/productCache.ts
import { unstable_cache } from 'next/cache'

export const getCachedProducts = unstable_cache(
  async (filters: ProductFilters) => {
    return await getProducts(filters)
  },
  ['products'],
  {
    revalidate: 300, // 5 دقائق
    tags: ['products']
  }
)

// إعادة تحديث التخزين المؤقت عند التغيير
export function revalidateProductsCache() {
  revalidateTag('products')
}
```

## 🧪 الاختبار | Testing Setup

### اختبارات الوحدة | Unit Tests

```typescript
// __tests__/productService.test.ts
import { getProducts, createProduct } from '@/lib/services/productService'

describe('Product Service', () => {
  test('should fetch products with filters', async () => {
    const products = await getProducts({ isActive: true })
    expect(products).toBeInstanceOf(Array)
    expect(products.every(p => p.isActive)).toBe(true)
  })
  
  test('should create product with valid data', async () => {
    const productData = {
      name: 'Test Product',
      category: 'test',
      productType: 'digital',
      processingType: 'instant',
      fields: [],
      packages: []
    }
    
    const product = await createProduct(productData)
    expect(product.id).toBeDefined()
    expect(product.name).toBe('Test Product')
  })
})
```

### اختبارات التكامل | Integration Tests

```typescript
// __tests__/integration/productFlow.test.ts
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ProductDashboard } from '@/components/admin/ProductDashboard'

describe('Product Management Flow', () => {
  test('should create product end-to-end', async () => {
    render(<ProductDashboard />)
    
    // النقر على إضافة منتج
    fireEvent.click(screen.getByText('إضافة منتج جديد'))
    
    // ملء النموذج
    fireEvent.change(screen.getByPlaceholderText('اسم المنتج'), {
      target: { value: 'PUBG Mobile UC' }
    })
    
    // حفظ المنتج
    fireEvent.click(screen.getByText('إنشاء المنتج'))
    
    // التحقق من النجاح
    await waitFor(() => {
      expect(screen.getByText('PUBG Mobile UC')).toBeInTheDocument()
    })
  })
})
```

## 📦 النشر | Deployment

### متغيرات الإنتاج | Production Variables

```bash
# .env.production
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-key
ENCRYPTION_SECRET_KEY=your-encryption-key
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com
```

### أوامر النشر | Deployment Commands

```bash
# بناء التطبيق
npm run build

# اختبار البناء محلياً
npm run start

# نشر على Vercel
vercel --prod

# نشر قاعدة البيانات
supabase db push
```

---

**🔧 دليل شامل للتكامل التقني مع نظام إدارة المنتجات**
