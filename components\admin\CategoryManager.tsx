"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Edit,
  Trash2,
  FolderOpen,
  Package
} from "lucide-react"
import { ProductCategory } from "@/lib/types"
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory
} from "@/lib/services/categoryService"
import { ImageUploader } from "./ImageUploader"
import { showSuc<PERSON>, showError, showWarning } from "@/components/ui/notification"

interface CategoryManagerProps {
  onCategoryChange?: () => void
}

export function CategoryManager({ onCategoryChange }: CategoryManagerProps) {
  const [categories, setCategories] = useState<ProductCategory[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null)
  const [categoryForm, setCategoryForm] = useState({
    name: "",
    image: ""
  })

  // Load categories on mount
  useEffect(() => {
    loadCategories()
  }, [])

  const loadCategories = () => {
    try {
      const loadedCategories = getCategories()
      setCategories(loadedCategories)
    } catch (error) {
      showError("خطأ في التحميل", "فشل في تحميل الفئات")
    }
  }

  const resetForm = () => {
    setCategoryForm({ name: "", image: "" })
    setEditingCategory(null)
  }

  const handleCreateCategory = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  const handleEditCategory = (category: ProductCategory) => {
    setCategoryForm({
      name: category.name,
      image: category.image
    })
    setEditingCategory(category)
    setIsDialogOpen(true)
  }

  const handleSaveCategory = () => {
    if (!categoryForm.name.trim()) {
      showWarning("اسم مطلوب", "يرجى إدخال اسم الفئة")
      return
    }

    if (!categoryForm.image.trim()) {
      showWarning("صورة مطلوبة", "يرجى اختيار صورة للفئة")
      return
    }

    try {
      if (editingCategory) {
        // Update existing category
        updateCategory(editingCategory.id, {
          name: categoryForm.name.trim(),
          image: categoryForm.image.trim()
        })
        showSuccess("تم التحديث", "تم تحديث الفئة بنجاح")
      } else {
        // Create new category
        createCategory(
          categoryForm.name.trim(),
          categoryForm.image.trim()
        )
        showSuccess("تم الإنشاء", "تم إنشاء الفئة بنجاح")
      }

      loadCategories()
      setIsDialogOpen(false)
      resetForm()
      onCategoryChange?.()
    } catch (error) {
      showError("خطأ في الحفظ", error instanceof Error ? error.message : "فشل في حفظ الفئة")
    }
  }

  const handleDeleteCategory = (category: ProductCategory) => {
    try {
      deleteCategory(category.id)
      showSuccess("تم الحذف", "تم حذف الفئة بنجاح")
      loadCategories()
      onCategoryChange?.()
    } catch (error) {
      showError("خطأ في الحذف", error instanceof Error ? error.message : "فشل في حذف الفئة")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FolderOpen className="h-5 w-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">إدارة الفئات</h3>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={handleCreateCategory} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة فئة
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingCategory ? "تعديل الفئة" : "إضافة فئة جديدة"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label className="text-slate-300">اسم الفئة *</Label>
                <Input
                  value={categoryForm.name}
                  onChange={(e) => setCategoryForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثال: PUBG Mobile"
                  className="bg-slate-600 border-slate-500 text-white"
                />
              </div>

              <ImageUploader
                value={categoryForm.image}
                onChange={(value) => setCategoryForm(prev => ({ ...prev, image: value }))}
                label="صورة الفئة"
                placeholder="اختر صورة للفئة..."
              />

              <div className="flex gap-2">
                <Button onClick={handleSaveCategory} className="flex-1">
                  {editingCategory ? "تحديث" : "إضافة"}
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  إلغاء
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Categories Grid */}
      {categories.length === 0 ? (
        <Card className="bg-slate-800/50 border-slate-700/50">
          <CardContent className="p-8 text-center">
            <FolderOpen className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد فئات</h3>
            <p className="text-slate-400 mb-4">
              ابدأ بإضافة فئات لتنظيم منتجاتك
            </p>
            <Button onClick={handleCreateCategory} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة فئة جديدة
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {categories.map((category) => (
            <Card key={category.id} className="bg-slate-800/50 border-slate-700/50 hover:border-slate-600 transition-colors">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-slate-600 rounded-lg flex items-center justify-center overflow-hidden">
                      <img
                        src={category.image}
                        alt={category.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div>
                      <h4 className="text-white font-medium">{category.name}</h4>
                      <p className="text-slate-400 text-xs">
                        {category.createdAt.toLocaleDateString('ar')}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditCategory(category)}
                    className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    تعديل
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent className="bg-slate-800 border-slate-700">
                      <AlertDialogHeader>
                        <AlertDialogTitle className="text-white">حذف الفئة</AlertDialogTitle>
                        <AlertDialogDescription className="text-slate-400">
                          هل أنت متأكد من حذف فئة "{category.name}"؟ 
                          <br />
                          <span className="text-yellow-400 text-sm">
                            تحذير: المنتجات المرتبطة بهذه الفئة ستحتاج إلى إعادة تصنيف.
                          </span>
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                          إلغاء
                        </AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDeleteCategory(category)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          حذف
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
