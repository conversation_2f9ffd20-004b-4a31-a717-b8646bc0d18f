"use client"

import React, { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  Type,
  Hash,
  Mail,
  ChevronDown,
  Package,
  PlusCircle,
  FileText,
  Image as ImageIcon,
  Minus
} from "lucide-react"
import { DynamicField, FieldType } from "@/lib/types"

interface FieldEditorProps {
  fields: DynamicField[]
  onFieldsUpdate: (fields: Dynamic<PERSON>ield[]) => void
}

export function FieldEditor({ fields, onFieldsUpdate }: FieldEditorProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingField, setEditingField] = useState<DynamicField | null>(null)
  const [fieldForm, setFieldForm] = useState({
    type: "text" as FieldType,
    name: "",
    label: "",
    placeholder: "",
    required: false,
    validation: {
      minLength: 0,
      maxLength: 0,
      pattern: "",
      min: 0,
      max: 0
    },
    options: [] as string[],
    dependsOn: "",
    isActive: true
  })
  const [newOption, setNewOption] = useState("")

  /**
   * Field type configurations with icons and descriptions
   */
  const fieldTypes = [
    {
      value: "universal_input",
      label: "نص",
      icon: Type,
      description: "حقل نص ذكي - بعد إنشاؤه يمكنك تحويله لإيميل، رقم هاتف، كلمة مرور، معرف لاعب، إلخ بنقرة واحدة!"
    },
    {
      value: "dropdown",
      label: "قائمة منسدلة",
      icon: ChevronDown,
      description: "قائمة اختيارات (مثل: الخادم، المنطقة، النظام)"
    }
  ]

  /**
   * Reset form to default values
   */
  const resetForm = () => {
    setFieldForm({
      type: "universal_input",
      name: "",
      label: "",
      placeholder: "",
      required: false,
      validation: {
        minLength: 0,
        maxLength: 0,
        pattern: "",
        min: 0,
        max: 0
      },
      options: [],
      dependsOn: "",
      isActive: true
    })
    setEditingField(null)
    setNewOption("")
  }

  /**
   * Generate internal field name from Arabic label
   */
  const generateFieldName = (label: string) => {
    // Simple transliteration for common Arabic gaming terms
    const transliterations: Record<string, string> = {
      "معرف اللعبة": "game_id",
      "اسم المستخدم": "username", 
      "الخادم": "server",
      "المنطقة": "region",
      "رقم الهاتف": "phone",
      "البريد الإلكتروني": "email",
      "الملاحظات": "notes",
      "كلمة المرور": "password"
    }
    
    if (transliterations[label]) {
      return transliterations[label]
    }
    
    // Fallback: create simple name
    return label.toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^\w_]/g, '')
      .substring(0, 20) || 'field'
  }

  /**
   * Open dialog for creating new field
   */
  const handleCreateField = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  /**
   * Open dialog for editing existing field
   */
  const handleEditField = (field: DynamicField) => {
    setFieldForm({
      type: field.type,
      name: field.name,
      label: field.label,
      placeholder: field.placeholder || "",
      required: field.required,
      validation: field.validation || {
        minLength: 0,
        maxLength: 0,
        pattern: "",
        min: 0,
        max: 0
      },
      options: field.options || [],
      dependsOn: field.dependsOn || "",
      isActive: field.isActive
    })
    setEditingField(field)
    setIsDialogOpen(true)
  }

  /**
   * Save field (create or update)
   */
  const handleSaveField = () => {
    // Basic validation
    if (!fieldForm.label.trim()) {
      alert("يرجى إدخال تسمية الحقل")
      return
    }

    if (fieldForm.type === "dropdown" && fieldForm.options.length === 0) {
      alert("يرجى إضافة خيارات للقائمة المنسدلة")
      return
    }

    const fieldName = fieldForm.name || generateFieldName(fieldForm.label)
    
    // Check for duplicate names (excluding current field when editing)
    const existingField = fields.find(f => 
      f.name === fieldName && (!editingField || f.id !== editingField.id)
    )
    if (existingField) {
      alert("يوجد حقل آخر بنفس الاسم الداخلي")
      return
    }

    const newField: DynamicField = {
      id: editingField?.id || generateId(),
      type: fieldForm.type,
      name: fieldName,
      label: fieldForm.label,
      placeholder: fieldForm.placeholder || undefined,
      required: fieldForm.required,
      validation: fieldForm.validation,
      options: fieldForm.type === "dropdown" ? fieldForm.options : undefined,
      dependsOn: fieldForm.dependsOn || undefined,
      sortOrder: editingField?.sortOrder || fields.length,
      isActive: fieldForm.isActive
    }

    let updatedFields: DynamicField[]
    
    if (editingField) {
      // Update existing field
      updatedFields = fields.map(field => 
        field.id === editingField.id ? newField : field
      )
    } else {
      // Add new field
      updatedFields = [...fields, newField]
    }

    onFieldsUpdate(updatedFields)
    setIsDialogOpen(false)
    resetForm()
  }

  /**
   * Delete field
   */
  const handleDeleteField = (fieldId: string) => {
    if (!confirm("هل أنت متأكد من حذف هذا الحقل؟")) return
    
    const updatedFields = fields.filter(field => field.id !== fieldId)
    onFieldsUpdate(updatedFields)
  }

  /**
   * Add option to dropdown field
   */
  const addOption = () => {
    if (newOption.trim()) {
      setFieldForm(prev => ({
        ...prev,
        options: [...prev.options, newOption.trim()]
      }))
      setNewOption("")
    }
  }

  /**
   * Remove option from dropdown field
   */
  const removeOption = (index: number) => {
    setFieldForm(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }))
  }

  /**
   * Get field type configuration
   */
  const getFieldTypeConfig = (type: FieldType) => {
    return fieldTypes.find(ft => ft.value === type)
  }

  /**
   * Generate unique ID
   */
  const generateId = () => {
    return Math.random().toString(36).substr(2, 9)
  }

  return (
    <Card className="bg-slate-700/50 border-slate-600">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center gap-2">
            <Type className="h-5 w-5" />
            الحقول المخصصة
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={handleCreateField} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                إضافة حقل
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-[95vw] sm:max-w-lg max-h-[90vh] overflow-y-auto p-4 sm:p-6">
              <DialogHeader className="pb-4">
                <DialogTitle className="text-lg sm:text-xl">
                  {editingField ? "تعديل الحقل" : "إضافة حقل جديد"}
                </DialogTitle>
              </DialogHeader>
              
              <div className="space-y-4">
                {/* Field Type Selection */}
                <div>
                  <Label className="text-slate-300">نوع الحقل</Label>
                  <Select value={fieldForm.type} onValueChange={(value: FieldType) => 
                    setFieldForm(prev => ({ ...prev, type: value, options: [] }))
                  }>
                    <SelectTrigger className="bg-slate-600 border-slate-500 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-600 border-slate-500">
                      {fieldTypes.map(type => {
                        const Icon = type.icon
                        return (
                          <SelectItem key={type.value} value={type.value}>
                            <div className="flex items-center gap-2">
                              <Icon className="h-4 w-4" />
                              {type.label}
                            </div>
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                  {getFieldTypeConfig(fieldForm.type) && (
                    <p className="text-xs text-slate-400 mt-1">
                      {getFieldTypeConfig(fieldForm.type)?.description}
                    </p>
                  )}
                </div>

                {/* Field Label */}
                <div>
                  <Label className="text-slate-300">التسمية *</Label>
                  <Input
                    value={fieldForm.label}
                    onChange={(e) => {
                      const label = e.target.value
                      setFieldForm(prev => ({
                        ...prev,
                        label,
                        name: prev.name || generateFieldName(label)
                      }))
                    }}
                    placeholder="معرف اللعبة"
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                </div>

                {/* Internal Name */}
                <div>
                  <Label className="text-slate-300">الاسم الداخلي</Label>
                  <Input
                    value={fieldForm.name}
                    onChange={(e) => setFieldForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="game_id"
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                  <p className="text-xs text-slate-400 mt-1">
                    يُستخدم في البرمجة - سيتم إنشاؤه تلقائياً إذا تُرك فارغاً
                  </p>
                </div>

                {/* Placeholder */}
                <div>
                  <Label className="text-slate-300">النص التوضيحي</Label>
                  <Input
                    value={fieldForm.placeholder}
                    onChange={(e) => setFieldForm(prev => ({ ...prev, placeholder: e.target.value }))}
                    placeholder="أدخل معرف اللعبة..."
                    className="bg-slate-600 border-slate-500 text-white"
                  />
                </div>

                {/* Dropdown Options */}
                {fieldForm.type === "dropdown" && (
                  <div>
                    <Label className="text-slate-300">خيارات القائمة المنسدلة</Label>
                    <div className="space-y-2">
                      <div className="flex gap-2">
                        <Input
                          value={newOption}
                          onChange={(e) => setNewOption(e.target.value)}
                          placeholder="أضف خيار جديد..."
                          className="bg-slate-600 border-slate-500 text-white"
                          onKeyPress={(e) => e.key === 'Enter' && addOption()}
                        />
                        <Button onClick={addOption} size="sm" className="bg-blue-600 hover:bg-blue-700">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="space-y-1">
                        {fieldForm.options.map((option, index) => (
                          <div key={index} className="flex items-center justify-between bg-slate-600 rounded px-3 py-2">
                            <span className="text-white">{option}</span>
                            <button
                              onClick={() => removeOption(index)}
                              className="text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Instructions for universal input */}
                {fieldForm.type === "universal_input" && (
                  <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4 space-y-3">
                    <div className="flex items-start gap-2">
                      <div className="text-blue-400 text-lg">💡</div>
                      <div>
                        <h4 className="text-blue-300 font-medium mb-2">كيفية تخصيص حقل النص:</h4>
                        <div className="text-blue-200 text-sm space-y-1">
                          <p><strong>1.</strong> أنشئ الحقل أولاً بالاسم والوصف</p>
                          <p><strong>2.</strong> بعد الإنشاء، ستظهر أزرار صغيرة بجانب اسم الحقل</p>
                          <p><strong>3.</strong> انقر على الزر المناسب لتحويل الحقل:</p>
                        </div>

                        <div className="grid grid-cols-2 gap-2 mt-3 text-xs">
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">email</code> → حقل إيميل مع تحقق
                          </div>
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">phone</code> → رقم هاتف مع لوحة أرقام
                          </div>
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">id</code> → معرف لاعب (أرقام وحروف)
                          </div>
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">password</code> → كلمة مرور مخفية
                          </div>
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">number</code> → أرقام فقط
                          </div>
                          <div className="bg-slate-700/50 rounded p-2">
                            <code className="text-yellow-300">text</code> → نص عادي
                          </div>
                        </div>

                        <div className="text-blue-300 text-sm mt-3 font-medium">
                          ✨ يمكنك تغيير النوع في أي وقت بنقرة واحدة!
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Switches */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-slate-300">حقل مطلوب</Label>
                    <Switch
                      checked={fieldForm.required}
                      onCheckedChange={(checked) => setFieldForm(prev => ({ ...prev, required: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-slate-300">نشط</Label>
                    <Switch
                      checked={fieldForm.isActive}
                      onCheckedChange={(checked) => setFieldForm(prev => ({ ...prev, isActive: checked }))}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsDialogOpen(false)}
                    className="border-slate-600 text-slate-300"
                  >
                    إلغاء
                  </Button>
                  <Button onClick={handleSaveField} className="bg-blue-600 hover:bg-blue-700">
                    {editingField ? "تحديث" : "إضافة"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        {fields.length === 0 ? (
          <div className="text-center py-8">
            <Type className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد حقول مخصصة</h3>
            <p className="text-slate-400 mb-4">أضف حقول مخصصة لجمع معلومات من العملاء</p>

            {/* Quick examples */}
            <div className="bg-slate-800/50 rounded-lg p-4 mb-4 text-left">
              <h4 className="text-slate-300 font-medium mb-2 text-center">أمثلة سريعة:</h4>
              <div className="space-y-2 text-sm text-slate-400">
                <div>🎮 <strong>معرف اللعبة:</strong> أنشئ حقل "نص" ← انقر <code className="bg-slate-700 px-1 rounded text-yellow-300">id</code></div>
                <div>📧 <strong>البريد الإلكتروني:</strong> أنشئ حقل "نص" ← انقر <code className="bg-slate-700 px-1 rounded text-blue-300">email</code></div>
                <div>📱 <strong>رقم الهاتف:</strong> أنشئ حقل "نص" ← انقر <code className="bg-slate-700 px-1 rounded text-green-300">phone</code></div>
                <div>🌍 <strong>اختيار الخادم:</strong> أنشئ حقل "قائمة منسدلة" ← أضف خيارات</div>
              </div>
            </div>

            <Button onClick={handleCreateField} className="bg-blue-600 hover:bg-blue-700">
              <Plus className="h-4 w-4 mr-2" />
              إضافة حقل جديد
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {fields.map((field) => {
              const typeConfig = getFieldTypeConfig(field.type)
              const Icon = typeConfig?.icon || Type
              
              return (
                <Card key={field.id} className="bg-slate-600/50 border-slate-500">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <GripVertical className="h-4 w-4 text-slate-500" />
                        <Icon className="h-5 w-5 text-blue-400" />
                        <div>
                          <h4 className="text-white font-medium">{field.label}</h4>
                          <div className="flex items-center gap-2 text-sm text-slate-400">
                            <span>{typeConfig?.label}</span>
                            <span>•</span>
                            <span>{field.name}</span>
                            {field.required && (
                              <>
                                <span>•</span>
                                <Badge variant="outline" className="text-xs border-red-500 text-red-400">
                                  مطلوب
                                </Badge>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant={field.isActive ? "default" : "secondary"} className="text-xs">
                          {field.isActive ? "نشط" : "غير نشط"}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditField(field)}
                          className="border-slate-500 text-slate-300 hover:bg-slate-700"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteField(field.id)}
                          className="border-red-600 text-red-400 hover:bg-red-600/10"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {field.placeholder && (
                      <p className="text-slate-400 text-sm mt-2 mr-8">
                        النص التوضيحي: "{field.placeholder}"
                      </p>
                    )}
                    
                    {field.options && field.options.length > 0 && (
                      <div className="mt-2 mr-8">
                        <p className="text-slate-400 text-sm mb-1">الخيارات:</p>
                        <div className="flex flex-wrap gap-1">
                          {field.options.map((option, index) => (
                            <Badge key={index} variant="outline" className="text-xs border-slate-500 text-slate-300">
                              {option}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
