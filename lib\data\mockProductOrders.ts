import { ProductOrder, OrderStatus, ProcessingType } from "@/lib/types"

// ## Mock product orders for testing
// ## Supabase Integration: This will be replaced with real database queries

export const mockProductOrders: ProductOrder[] = [
  {
    id: "PRD-20241225-160030-DIGITAL",
    type: "product_order",
    templateId: "template_4", // Our new digital template
    templateName: "حزمة PUBG Mobile UC الرقمية",
    templateCategory: "gaming",
    productData: {
      player_id: "*********",
      uc_package: {
        id: "uc_325",
        name: "325 UC",
        amount: "325 UC",
        price: 75,
        originalPrice: 85,
        discount: 12,
        popular: true,
        description: "الحزمة الأكثر شعبية"
      },
      quantity: 1,
      digital_content_delivered: true
    },
    pricing: {
      basePrice: 75,
      modifiers: [
        {
          fieldName: "uc_package",
          fieldLabel: "حزمة UC",
          modifier: 75,
          type: "override",
          source: "package"
        }
      ],
      quantity: 1,
      subtotal: 75,
      totalPrice: 75,
      currency: "USD"
    },
    userDetails: {
      fullName: "أحمد محمد الجامر",
      email: "<EMAIL>",
      phone: "+************"
    },
    status: "completed",
    processingType: "automatic", // Digital products are processed automatically
    adminNotes: "منتج رقمي - تم التسليم الفوري للكود",
    timeline: [
      {
        id: "event_1",
        type: "created",
        description: "تم إنشاء الطلب بنجاح",
        details: {
          templateName: "حزمة PUBG Mobile UC الرقمية",
          totalPrice: 75,
          currency: "USD",
          productType: "digital"
        },
        createdAt: new Date("2024-12-25T16:00:30Z"),
        createdBy: "user"
      },
      {
        id: "event_2",
        type: "payment_confirmed",
        description: "تم تأكيد الدفع بنجاح",
        details: {
          paymentMethod: "wallet",
          amount: 75,
          currency: "USD"
        },
        createdAt: new Date("2024-12-25T16:00:45Z"),
        createdBy: "system"
      },
      {
        id: "event_3",
        type: "digital_content_delivered",
        description: "تم تسليم المحتوى الرقمي فوراً",
        details: {
          contentType: "game_code",
          deliveryMethod: "instant",
          codeId: "code_uc_002"
        },
        createdAt: new Date("2024-12-25T16:00:50Z"),
        createdBy: "system"
      },
      {
        id: "event_4",
        type: "completed",
        description: "تم إكمال الطلب بنجاح",
        details: {
          completionTime: "20 ثانية",
          digitalContentDelivered: true
        },
        createdAt: new Date("2024-12-25T16:00:50Z"),
        createdBy: "system"
      }
    ],
    createdAt: new Date("2024-12-25T16:00:30Z"),
    updatedAt: new Date("2024-12-25T16:00:50Z"),
    completedAt: new Date("2024-12-25T16:00:50Z"),
    userId: "user_gamer_123",
    priority: "normal"
  },
  {
    id: "PRD-********-143022-1234",
    type: "product_order",
    templateId: "template_free_fire",
    templateName: "شحن جواهر Free Fire",
    templateCategory: "gaming",
    productData: {
      player_id: "*********",
      package: {
        id: "pkg_500",
        name: "500 جوهرة",
        amount: "500",
        price: 100,
        description: "حزمة متوسطة مناسبة للاعبين"
      },
      quantity: 2,
      server: "middle_east",
      account_type: "guest",
      terms_accepted: true
    },
    pricing: {
      basePrice: 100,
      modifiers: [
        {
          fieldName: "package",
          fieldLabel: "حزمة الجواهر",
          modifier: 100,
          type: "override",
          source: "package"
        }
      ],
      quantity: 2,
      subtotal: 100,
      totalPrice: 200,
      currency: "SDG"
    },
    userDetails: {
      fullName: "أحمد محمد علي",
      email: "<EMAIL>",
      phone: "+************"
    },
    status: "pending",
    processingType: "manual",
    timeline: [
      {
        id: "event_1",
        type: "created",
        description: "تم إنشاء الطلب بنجاح",
        details: {
          templateName: "شحن جواهر Free Fire",
          totalPrice: 200,
          currency: "SDG"
        },
        createdAt: new Date("2024-12-05T14:30:22Z"),
        createdBy: "user"
      }
    ],
    createdAt: new Date("2024-12-05T14:30:22Z"),
    updatedAt: new Date("2024-12-05T14:30:22Z"),
    userId: "user_123",
    priority: "normal"
  },
  {
    id: "PRD-********-120015-5678",
    type: "product_order",
    templateId: "template_pubg_mobile",
    templateName: "شحن UC PUBG Mobile",
    templateCategory: "gaming",
    productData: {
      player_id: "*********",
      package: {
        id: "pkg_1800",
        name: "1800 UC",
        amount: "1800",
        price: 250,
        originalPrice: 300,
        popular: true,
        description: "الحزمة الأكثر شعبية"
      },
      quantity: 1,
      server: "global",
      account_type: "premium",
      special_instructions: "يرجى المعالجة خلال المساء"
    },
    pricing: {
      basePrice: 250,
      modifiers: [
        {
          fieldName: "package",
          fieldLabel: "حزمة UC",
          modifier: 250,
          type: "override",
          source: "package"
        }
      ],
      quantity: 1,
      subtotal: 250,
      totalPrice: 250,
      currency: "SDG"
    },
    userDetails: {
      fullName: "فاطمة أحمد محمد",
      email: "<EMAIL>",
      phone: "+************"
    },
    status: "processing",
    processingType: "manual",
    adminNotes: "تم التحقق من البيانات، جاري المعالجة",
    timeline: [
      {
        id: "event_1",
        type: "created",
        description: "تم إنشاء الطلب بنجاح",
        details: {
          templateName: "شحن UC PUBG Mobile",
          totalPrice: 250,
          currency: "SDG"
        },
        createdAt: new Date("2024-12-05T12:00:15Z"),
        createdBy: "user"
      },
      {
        id: "event_2",
        type: "status_change",
        description: "تم تغيير حالة الطلب من \"في الانتظار\" إلى \"قيد المعالجة\"",
        details: {
          previousStatus: "pending",
          newStatus: "processing",
          adminNotes: "تم التحقق من البيانات، جاري المعالجة"
        },
        createdAt: new Date("2024-12-05T12:15:30Z"),
        createdBy: "admin_001"
      }
    ],
    createdAt: new Date("2024-12-05T12:00:15Z"),
    updatedAt: new Date("2024-12-05T12:15:30Z"),
    userId: "user_456",
    assignedAdminId: "admin_001",
    priority: "high"
  },
  {
    id: "PRD-20241204-180045-9012",
    type: "product_order",
    templateId: "template_tiktok_coins",
    templateName: "شحن عملات TikTok",
    templateCategory: "social",
    productData: {
      username: "@user123",
      package: {
        id: "pkg_1000",
        name: "1000 عملة",
        amount: "1000",
        price: 50,
        description: "حزمة أساسية للمبتدئين"
      },
      quantity: 1,
      payment_method: "instant",
      terms_accepted: true
    },
    pricing: {
      basePrice: 50,
      modifiers: [
        {
          fieldName: "package",
          fieldLabel: "حزمة العملات",
          modifier: 50,
          type: "override",
          source: "package"
        }
      ],
      quantity: 1,
      subtotal: 50,
      totalPrice: 50,
      currency: "SDG"
    },
    userDetails: {
      fullName: "محمد عبدالله حسن",
      email: "<EMAIL>",
      phone: "+249555123456"
    },
    status: "completed",
    processingType: "instant",
    adminNotes: "تم الإنجاز بنجاح",
    timeline: [
      {
        id: "event_1",
        type: "created",
        description: "تم إنشاء الطلب بنجاح",
        details: {
          templateName: "شحن عملات TikTok",
          totalPrice: 50,
          currency: "SDG"
        },
        createdAt: new Date("2024-12-04T18:00:45Z"),
        createdBy: "user"
      },
      {
        id: "event_2",
        type: "status_change",
        description: "تم تغيير حالة الطلب من \"في الانتظار\" إلى \"قيد المعالجة\"",
        details: {
          previousStatus: "pending",
          newStatus: "processing"
        },
        createdAt: new Date("2024-12-04T18:01:00Z"),
        createdBy: "system"
      },
      {
        id: "event_3",
        type: "status_change",
        description: "تم تغيير حالة الطلب من \"قيد المعالجة\" إلى \"مكتمل\"",
        details: {
          previousStatus: "processing",
          newStatus: "completed",
          adminNotes: "تم الإنجاز بنجاح"
        },
        createdAt: new Date("2024-12-04T18:05:30Z"),
        createdBy: "system"
      }
    ],
    createdAt: new Date("2024-12-04T18:00:45Z"),
    updatedAt: new Date("2024-12-04T18:05:30Z"),
    completedAt: new Date("2024-12-04T18:05:30Z"),
    userId: "user_789",
    priority: "normal"
  },
  {
    id: "PRD-********-093012-3456",
    type: "product_order",
    templateId: "template_pes_mobile",
    templateName: "شحن عملات PES Mobile",
    templateCategory: "gaming",
    productData: {
      player_id: "PES123456",
      package: {
        id: "pkg_2500",
        name: "2500 عملة",
        amount: "2500",
        price: 150,
        description: "حزمة كبيرة للاعبين المحترفين"
      },
      quantity: 1,
      server: "asia",
      account_type: "vip",
      terms_accepted: true
    },
    pricing: {
      basePrice: 150,
      modifiers: [
        {
          fieldName: "package",
          fieldLabel: "حزمة العملات",
          modifier: 150,
          type: "override",
          source: "package"
        }
      ],
      quantity: 1,
      subtotal: 150,
      totalPrice: 150,
      currency: "SDG"
    },
    userDetails: {
      fullName: "سارة محمد أحمد",
      email: "<EMAIL>",
      phone: "+************"
    },
    status: "failed",
    processingType: "manual",
    adminNotes: "فشل في التحقق من معرف اللاعب",
    internalNotes: "معرف اللاعب غير صحيح أو غير موجود",
    timeline: [
      {
        id: "event_1",
        type: "created",
        description: "تم إنشاء الطلب بنجاح",
        details: {
          templateName: "شحن عملات PES Mobile",
          totalPrice: 150,
          currency: "SDG"
        },
        createdAt: new Date("2024-12-03T09:30:12Z"),
        createdBy: "user"
      },
      {
        id: "event_2",
        type: "status_change",
        description: "تم تغيير حالة الطلب من \"في الانتظار\" إلى \"قيد المعالجة\"",
        details: {
          previousStatus: "pending",
          newStatus: "processing"
        },
        createdAt: new Date("2024-12-03T10:00:00Z"),
        createdBy: "admin_002"
      },
      {
        id: "event_3",
        type: "status_change",
        description: "تم تغيير حالة الطلب من \"قيد المعالجة\" إلى \"فشل\"",
        details: {
          previousStatus: "processing",
          newStatus: "failed",
          adminNotes: "فشل في التحقق من معرف اللاعب"
        },
        createdAt: new Date("2024-12-03T10:30:45Z"),
        createdBy: "admin_002"
      }
    ],
    createdAt: new Date("2024-12-03T09:30:12Z"),
    updatedAt: new Date("2024-12-03T10:30:45Z"),
    userId: "user_101",
    assignedAdminId: "admin_002",
    priority: "normal"
  }
]

// ## Initialize localStorage with mock data if empty
export function initializeMockProductOrders(): void {
  if (typeof window === "undefined") return
  
  try {
    const existing = localStorage.getItem("product_orders")
    if (!existing) {
      localStorage.setItem("product_orders", JSON.stringify(mockProductOrders))
      // Mock data initialized in localStorage
    }
  } catch (error) {
    // Error initializing mock data - handle silently
  }
}
