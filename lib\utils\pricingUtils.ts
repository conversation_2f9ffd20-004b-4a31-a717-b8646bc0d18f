import { ProductTemplate, ProductPackage, Currency } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"

/**
 * Unified pricing calculation utilities
 * Used across admin, shop, and wallet systems for consistency
 */

/**
 * Get the starting price for a product (lowest active package price or base price)
 * Used in shop display and product listings
 */
export function getProductStartingPrice(product: ProductTemplate): number {
  if (product.packages && product.packages.length > 0) {
    const activePrices = product.packages
      .filter(pkg => pkg.isActive)
      .map(pkg => pkg.price)
    return activePrices.length > 0 ? Math.min(...activePrices) : 0
  }
  return product.basePrice || 0
}

/**
 * Get the price range for a product (min - max)
 * Used in product displays to show price ranges
 */
export function getProductPriceRange(product: ProductTemplate): { min: number; max: number } {
  if (product.packages && product.packages.length > 0) {
    const activePrices = product.packages
      .filter(pkg => pkg.isActive)
      .map(pkg => pkg.price)
    
    if (activePrices.length > 0) {
      return {
        min: Math.min(...activePrices),
        max: Math.max(...activePrices)
      }
    }
  }
  
  const basePrice = product.basePrice || 0
  return { min: basePrice, max: basePrice }
}

/**
 * Format price range for display
 * Used in shop and admin displays
 */
export function formatPriceRange(
  product: ProductTemplate, 
  currency: Currency = "USD",
  formatPrice: (amount: number) => string
): string {
  const { min, max } = getProductPriceRange(product)
  
  if (min === max) {
    return formatPrice(min)
  }
  
  return `${formatPrice(min)} - ${formatPrice(max)}`
}

/**
 * Check if a product has discounted packages
 * Used for showing discount badges
 */
export function hasDiscountedPackages(product: ProductTemplate): boolean {
  if (!product.packages || product.packages.length === 0) {
    return false
  }
  
  return product.packages.some(pkg => 
    pkg.isActive && pkg.originalPrice && pkg.originalPrice > pkg.price
  )
}

/**
 * Get the best discount percentage for a product
 * Used for discount badges and promotions
 */
export function getBestDiscountPercentage(product: ProductTemplate): number {
  if (!product.packages || product.packages.length === 0) {
    return 0
  }
  
  const discounts = product.packages
    .filter(pkg => pkg.isActive && pkg.originalPrice && pkg.originalPrice > pkg.price)
    .map(pkg => {
      const discount = ((pkg.originalPrice! - pkg.price) / pkg.originalPrice!) * 100
      return Math.round(discount)
    })
  
  return discounts.length > 0 ? Math.max(...discounts) : 0
}

/**
 * Check if a product is popular (has popular packages or is featured)
 * Used for popular badges and sorting
 */
export function isProductPopular(product: ProductTemplate): boolean {
  if (product.packages && product.packages.length > 0) {
    return product.packages.some(pkg => pkg.popular && pkg.isActive)
  }
  return product.isFeatured || false
}

/**
 * Get the most popular package for a product
 * Used for default selections and recommendations
 */
export function getPopularPackage(product: ProductTemplate): ProductPackage | null {
  if (!product.packages || product.packages.length === 0) {
    return null
  }
  
  const popularPackages = product.packages.filter(pkg => pkg.popular && pkg.isActive)
  if (popularPackages.length > 0) {
    // Return the cheapest popular package
    return popularPackages.reduce((cheapest, current) => 
      current.price < cheapest.price ? current : cheapest
    )
  }
  
  // If no popular packages, return the cheapest active package
  const activePackages = product.packages.filter(pkg => pkg.isActive)
  if (activePackages.length > 0) {
    return activePackages.reduce((cheapest, current) => 
      current.price < cheapest.price ? current : cheapest
    )
  }
  
  return null
}

/**
 * Calculate package savings compared to original price
 * Used for savings displays and promotions
 */
export function calculatePackageSavings(pkg: ProductPackage): number {
  if (!pkg.originalPrice || pkg.originalPrice <= pkg.price) {
    return 0
  }
  return pkg.originalPrice - pkg.price
}

/**
 * Get package discount percentage
 * Used for discount badges on individual packages
 */
export function getPackageDiscountPercentage(pkg: ProductPackage): number {
  if (!pkg.originalPrice || pkg.originalPrice <= pkg.price) {
    return 0
  }
  return Math.round(((pkg.originalPrice - pkg.price) / pkg.originalPrice) * 100)
}

/**
 * Sort packages by price (ascending)
 * Used for consistent package ordering
 */
export function sortPackagesByPrice(packages: ProductPackage[]): ProductPackage[] {
  return [...packages].sort((a, b) => a.price - b.price)
}

/**
 * Sort packages by popularity and price
 * Used for optimal package ordering in displays
 */
export function sortPackagesByPopularity(packages: ProductPackage[]): ProductPackage[] {
  return [...packages].sort((a, b) => {
    // Popular packages first
    if (a.popular && !b.popular) return -1
    if (!a.popular && b.popular) return 1
    
    // Then by price (ascending)
    return a.price - b.price
  })
}

/**
 * Validate package pricing
 * Used in admin forms for price validation
 */
export function validatePackagePricing(pkg: ProductPackage): string[] {
  const errors: string[] = []
  
  if (pkg.price <= 0) {
    errors.push("السعر يجب أن يكون أكبر من صفر")
  }
  
  if (pkg.originalPrice && pkg.originalPrice <= pkg.price) {
    errors.push("السعر الأصلي يجب أن يكون أكبر من السعر الحالي")
  }
  
  if (pkg.discount && (pkg.discount < 0 || pkg.discount > 100)) {
    errors.push("نسبة الخصم يجب أن تكون بين 0 و 100")
  }
  
  return errors
}
