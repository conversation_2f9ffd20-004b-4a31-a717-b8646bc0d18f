"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  Search,
  Filter,
  Star,
  Clock,
  Zap,
  Gamepad2,
  CreditCard,
  Gift,
  MessageCircle,
  Sparkles,
  Key,
  Package
} from "lucide-react"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"
import { ProductTemplate } from "@/lib/types"
import { useProducts, useAppLoading } from "@/lib/stores/appStore"
import { getProductStartingPrice, isProductPopular } from "@/lib/utils/pricingUtils"

// Enhanced categories with icons
const categories = [
  { id: "all", label: "جميع المنتجات", icon: <Gift className="h-4 w-4" /> },
  { id: "ألعاب الموبايل", label: "ألعاب الموبايل", icon: <Gamepad2 className="h-4 w-4" /> },
  { id: "منصات التواصل", label: "منصات التواصل", icon: <MessageCircle className="h-4 w-4" /> },
  { id: "بطاقات الألعاب", label: "بطاقات الألعاب", icon: <CreditCard className="h-4 w-4" /> },
  { id: "digital", label: "🎮 منتجات رقمية", icon: <Key className="h-4 w-4" /> }
]

export default function ShopPage() {
  const [activeTab, setActiveTab] = useState("home")
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const router = useRouter()

  // Use global currency context for price conversion
  const { formatPrice } = useCurrencyConverter()

  // Get products from global store (loaded on app startup)
  const products = useProducts()
  const isLoading = useAppLoading()

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Product click handler
  const handleProductClick = (productId: string) => {
    router.push(`/shop/${productId}`)
  }

  // Filter products based on search and category
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()))

    // Handle category filtering including digital products
    let matchesCategory = false
    if (selectedCategory === "all") {
      matchesCategory = true
    } else if (selectedCategory === "digital") {
      matchesCategory = product.productType === "digital"
    } else {
      matchesCategory = product.category === selectedCategory
    }

    return matchesSearch && matchesCategory
  })



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />
      </div>

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            متجر الألعاب
          </h1>
          <p className="text-slate-300 text-lg">
            اكتشف أفضل العروض لشحن ألعابك المفضلة
          </p>
        </div>

        {/* Search and Filters */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Search */}
          <div className="lg:col-span-2">
            <div className="relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
              <Input
                placeholder="ابحث عن لعبتك المفضلة..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pr-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="relative">
            <Filter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full pr-10 pl-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:border-yellow-400 focus:outline-none appearance-none"
            >
              {categories.map((category) => (
                <option key={category.id} value={category.id} className="bg-slate-800">
                  {category.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Category Pills */}
        <div className="flex flex-wrap gap-3 mb-8">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 ${
                selectedCategory === category.id
                  ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-0"
                  : "border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400"
              }`}
            >
              {category.icon}
              {category.label}
            </Button>
          ))}
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⏳</div>
            <h3 className="text-xl font-bold text-white mb-2">جاري تحميل المنتجات...</h3>
            <p className="text-slate-400">يرجى الانتظار</p>
          </div>
        ) : (
          <>
            {/* Products Grid - Enhanced with animations and better styling */}
            <div className="grid grid-cols-2 gap-4 md:gap-6">
              {filteredProducts.map((product, index) => (
                <Card
                  key={product.id}
                  onClick={() => handleProductClick(product.id)}
                  className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 hover:border-yellow-400/30 hover:shadow-2xl hover:shadow-yellow-400/10 transition-all duration-500 cursor-pointer group overflow-hidden transform hover:scale-105 hover:-translate-y-2"
                  style={{
                    animationDelay: `${index * 100}ms`
                  }}
                >
                  <CardContent className="p-0">
                    {/* Product Image */}
                    <div className="relative aspect-square overflow-hidden">
                      {product.previewImage ? (
                        <img
                          src={product.previewImage}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center relative overflow-hidden">
                          {/* Animated background pattern */}
                          <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 group-hover:from-yellow-400/10 group-hover:to-orange-500/10 transition-all duration-500"></div>
                          <Gamepad2 className="h-12 w-12 text-slate-400 group-hover:text-yellow-400 transition-colors duration-300 relative z-10" />
                        </div>
                      )}

                      {/* Digital Product Badge with sparkle animation */}
                      {product.productType === "digital" && (
                        <div className="absolute top-3 right-3">
                          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse">
                            <Sparkles className="h-3 w-3 mr-1" />
                            أكواد فورية
                          </Badge>
                        </div>
                      )}

                      {/* Popular Badge with animation */}
                      {isProductPopular(product) && product.productType !== "digital" && (
                        <div className="absolute top-3 right-3 animate-pulse">
                          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg">
                            🔥 الأكثر طلباً
                          </Badge>
                        </div>
                      )}

                      {/* Popular + Digital Badge */}
                      {isProductPopular(product) && product.productType === "digital" && (
                        <div className="absolute top-3 right-3 space-y-1">
                          <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse block">
                            <Sparkles className="h-3 w-3 mr-1" />
                            أكواد فورية
                          </Badge>
                          <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg block">
                            🔥 الأكثر طلباً
                          </Badge>
                        </div>
                      )}

                      {/* Processing Type Badge */}
                      <div className="absolute top-3 left-3">
                        <Badge
                          variant="outline"
                          className={`text-xs font-medium border-0 ${
                            product.processingType === "instant"
                              ? 'bg-green-500/20 text-green-400'
                              : 'bg-blue-500/20 text-blue-400'
                          }`}
                        >
                          {product.processingType === "instant" ? (
                            <>
                              <Zap className="h-3 w-3 mr-1" />
                              {product.estimatedTime || "فوري"}
                            </>
                          ) : (
                            <>
                              <Clock className="h-3 w-3 mr-1" />
                              {product.estimatedTime || "يدوي"}
                            </>
                          )}
                        </Badge>
                      </div>

                      {/* Hover overlay with price info */}
                      <div className="absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                        <div className="p-3 w-full">
                          <div className="flex items-center justify-between text-white">
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 text-yellow-400 fill-current" />
                              <span className="text-xs font-medium">4.8</span>
                              <span className="text-xs text-slate-300">(جديد)</span>
                            </div>
                            <div className="text-xs font-bold text-yellow-400">
                              من {formatPrice(getProductStartingPrice(product))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-4">
                      <h3 className="text-white font-bold text-center group-hover:text-yellow-400 transition-colors duration-300 text-sm leading-tight">
                        {product.name}
                      </h3>
                      <p className="text-slate-400 text-xs text-center mt-2 line-clamp-2 group-hover:text-slate-300 transition-colors duration-300">
                        {product.description || "منتج رائع من متجر الراية"}
                      </p>

                      {/* Quick info */}
                      <div className="flex items-center justify-center gap-2 mt-3 text-xs">
                        <div className="flex items-center gap-1 text-slate-500">
                          <Clock className="h-3 w-3" />
                          <span>{product.estimatedTime}</span>
                        </div>
                        <span className="text-slate-600">•</span>
                        <div className="flex items-center gap-1 text-slate-500">
                          <Package className="h-3 w-3" />
                          <span>{product.packages.length} حزمة</span>
                        </div>
                      </div>
                        </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State */}
            {filteredProducts.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-bold text-white mb-2">لا توجد منتجات</h3>
                <p className="text-slate-400">
                  {products.length === 0
                    ? "لم يتم إنشاء أي منتجات بعد. قم بإنشاء منتجات من لوحة الإدارة."
                    : "لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى."
                  }
                </p>
              </div>
            )}
          </>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
