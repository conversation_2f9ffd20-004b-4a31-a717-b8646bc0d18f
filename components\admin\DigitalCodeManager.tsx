"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Plus,
  Upload,
  Download,
  Eye,
  EyeOff,
  Trash2,
  Key,
  Shield,
  Co<PERSON>,
  <PERSON>freshC<PERSON>,
  AlertTriangle
} from "lucide-react"
import { ProductPackage, DigitalCode, CodeStatus } from "@/lib/types"
import {
  encryptCode,
  decryptCode,
  maskCode,
  validateCodeFormat,
  generateRandomCodes,
  formatCodeForDisplay,
  cleanCode
} from "@/lib/utils/encryption"
import { showSuccess, showError, showWarning } from "@/components/ui/notification"

interface DigitalCodeManagerProps {
  package: ProductPackage
  onCodesUpdate: (codes: string[]) => void
}

export function DigitalCodeManager({ package: pkg, onCodesUpdate }: DigitalCodeManagerProps) {
  const [codes, setCodes] = useState<DigitalCode[]>([])
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [bulkCodes, setBulkCodes] = useState("")
  const [showCodes, setShowCodes] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Load codes from localStorage on mount
  useEffect(() => {
    loadCodes()
  }, [pkg.id])

  const loadCodes = () => {
    const storageKey = `digital_codes_${pkg.id}`
    const storedCodes = JSON.parse(localStorage.getItem(storageKey) || '[]')
    setCodes(storedCodes.map((code: any) => ({
      ...code,
      createdAt: new Date(code.createdAt),
      usedAt: code.usedAt ? new Date(code.usedAt) : undefined,
      expiresAt: code.expiresAt ? new Date(code.expiresAt) : undefined
    })))
  }

  const saveCodes = (newCodes: DigitalCode[]) => {
    const storageKey = `digital_codes_${pkg.id}`
    localStorage.setItem(storageKey, JSON.stringify(newCodes))
    setCodes(newCodes)
    
    // Update parent component with encrypted codes
    const encryptedCodes = newCodes.map(code => code.code)
    onCodesUpdate(encryptedCodes)
  }

  const handleAddSingleCode = () => {
    const code = prompt("أدخل الكود الرقمي:")
    if (!code) return

    const cleanedCode = cleanCode(code)
    if (!validateCodeFormat(cleanedCode)) {
      showError("تنسيق غير صحيح", "يرجى التأكد من تنسيق الكود")
      return
    }

    try {
      const encryptedCode = encryptCode(cleanedCode)
      const newCode: DigitalCode = {
        id: generateId(),
        code: encryptedCode,
        isUsed: false,
        isReserved: false,
        createdAt: new Date()
      }

      saveCodes([...codes, newCode])
      showSuccess("تم إضافة الكود", "تم إضافة الكود بنجاح وتشفيره")
    } catch (error) {
      showError("فشل في التشفير", "حدث خطأ أثناء تشفير الكود")
    }
  }

  const handleBulkAdd = () => {
    if (!bulkCodes.trim()) {
      alert("يرجى إدخال الأكواد")
      return
    }

    setIsLoading(true)
    try {
      const codeLines = bulkCodes.split('\n').filter(line => line.trim())
      const validCodes: DigitalCode[] = []
      const invalidCodes: string[] = []

      codeLines.forEach(line => {
        const cleanedCode = cleanCode(line.trim())
        if (validateCodeFormat(cleanedCode)) {
          try {
            const encryptedCode = encryptCode(cleanedCode)
            validCodes.push({
              id: generateId(),
              code: encryptedCode,
              isUsed: false,
              isReserved: false,
              createdAt: new Date()
            })
          } catch {
            invalidCodes.push(line)
          }
        } else {
          invalidCodes.push(line)
        }
      })

      if (validCodes.length > 0) {
        saveCodes([...codes, ...validCodes])
        setBulkCodes("")
        setIsDialogOpen(false)
        showSuccess("تم إضافة الأكواد", `تم إضافة ${validCodes.length} كود بنجاح`)
      }

      if (invalidCodes.length > 0) {
        showWarning("أكواد غير صحيحة", `فشل في إضافة ${invalidCodes.length} كود بسبب تنسيق غير صحيح`)
      }
    } catch (error) {
      showError("خطأ في الإضافة", "حدث خطأ أثناء إضافة الأكواد")
    } finally {
      setIsLoading(false)
    }
  }



  const handleDeleteCode = (codeId: string) => {
    const updatedCodes = codes.filter(code => code.id !== codeId)
    saveCodes(updatedCodes)
  }

  const handleCopyCode = async (code: DigitalCode) => {
    try {
      const decryptedCode = decryptCode(code.code)
      await navigator.clipboard.writeText(decryptedCode)
      showSuccess("تم النسخ", "تم نسخ الكود إلى الحافظة")
    } catch (error) {
      showError("فشل في النسخ", "لم نتمكن من نسخ الكود")
    }
  }

  const getCodeDisplay = (code: DigitalCode) => {
    if (showCodes) {
      try {
        const decryptedCode = decryptCode(code.code)
        return formatCodeForDisplay(decryptedCode)
      } catch {
        return "خطأ في فك التشفير"
      }
    }
    return maskCode("XXXXXXXXXXXX")
  }

  const availableCodes = codes.filter(code => !code.isUsed && !code.isReserved)
  const usedCodes = codes.filter(code => code.isUsed)
  const reservedCodes = codes.filter(code => code.isReserved && !code.isUsed)

  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2, 9)

  return (
    <Card className="bg-slate-700/50 border-slate-600">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="text-white flex items-center gap-2">
            <Key className="h-5 w-5" />
            إدارة الأكواد الرقمية - {pkg.name}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCodes(!showCodes)}
              className="border-slate-600 text-slate-300"
            >
              {showCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showCodes ? "إخفاء" : "إظهار"}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Statistics */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-3 text-center">
            <div className="text-green-400 font-bold text-lg">{availableCodes.length}</div>
            <div className="text-green-300 text-sm">متاح</div>
          </div>
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3 text-center">
            <div className="text-yellow-400 font-bold text-lg">{reservedCodes.length}</div>
            <div className="text-yellow-300 text-sm">محجوز</div>
          </div>
          <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-3 text-center">
            <div className="text-red-400 font-bold text-lg">{usedCodes.length}</div>
            <div className="text-red-300 text-sm">مستخدم</div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={handleAddSingleCode} className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            إضافة كود واحد
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-slate-600 text-slate-300">
                <Upload className="h-4 w-4 mr-2" />
                إضافة متعددة
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
              <DialogHeader>
                <DialogTitle>إضافة أكواد متعددة</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>الأكواد (كود واحد في كل سطر)</Label>
                  <Textarea
                    value={bulkCodes}
                    onChange={(e) => setBulkCodes(e.target.value)}
                    placeholder="ABC123DEF456&#10;GHI789JKL012&#10;MNO345PQR678"
                    className="bg-slate-700 border-slate-600 text-white min-h-[120px]"
                    rows={6}
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleBulkAdd} disabled={isLoading} className="flex-1">
                    {isLoading ? "جاري الإضافة..." : "إضافة الأكواد"}
                  </Button>
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    إلغاء
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>


        </div>

        {/* Codes List */}
        {codes.length === 0 ? (
          <div className="text-center py-8">
            <Key className="h-16 w-16 text-slate-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">لا توجد أكواد</h3>
            <p className="text-slate-400 mb-4">ابدأ بإضافة أكواد رقمية لهذه الحزمة</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {codes.map((code) => (
              <div
                key={code.id}
                className={`flex items-center justify-between p-3 rounded-lg border ${
                  code.isUsed
                    ? 'bg-red-500/10 border-red-500/30'
                    : code.isReserved
                    ? 'bg-yellow-500/10 border-yellow-500/30'
                    : 'bg-green-500/10 border-green-500/30'
                }`}
              >
                <div className="flex items-center gap-3">
                  <Shield className="h-4 w-4 text-slate-400" />
                  <div>
                    <div className="text-white font-mono text-sm">
                      {getCodeDisplay(code)}
                    </div>
                    <div className="text-slate-400 text-xs">
                      {code.createdAt.toLocaleDateString('ar')}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge
                    className={
                      code.isUsed
                        ? 'bg-red-500/20 text-red-400'
                        : code.isReserved
                        ? 'bg-yellow-500/20 text-yellow-400'
                        : 'bg-green-500/20 text-green-400'
                    }
                  >
                    {code.isUsed ? 'مستخدم' : code.isReserved ? 'محجوز' : 'متاح'}
                  </Badge>

                  {!code.isUsed && !code.isReserved && (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleCopyCode(code)}
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className="border-red-600 text-red-400 hover:bg-red-600/10"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-slate-800 border-slate-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle className="text-white">حذف الكود</AlertDialogTitle>
                            <AlertDialogDescription className="text-slate-400">
                              هل أنت متأكد من حذف هذا الكود؟ لا يمكن التراجع عن هذا الإجراء.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-slate-700 text-white border-slate-600">
                              إلغاء
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteCode(code.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              حذف
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Warning for low stock */}
        {availableCodes.length < 5 && codes.length > 0 && (
          <div className="bg-yellow-500/20 border border-yellow-500/30 rounded-lg p-3 flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-400" />
            <div className="text-yellow-300 text-sm">
              تحذير: عدد الأكواد المتاحة قليل ({availableCodes.length} أكواد متبقية)
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
