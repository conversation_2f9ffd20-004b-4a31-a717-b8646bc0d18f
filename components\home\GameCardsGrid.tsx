import { useGameCards } from "@/lib/stores/appStore"
import { useRouter } from "next/navigation"

export function GameCardsGrid() {
  const router = useRouter()
  const gameCards = useGameCards()

  // Map game cards to actual product routes
  const getProductRoute = (cardTitle: string) => {
    switch (cardTitle) {
      case "FREE FIRE":
        return "/shop/free-fire-diamonds"
      case "PUBG Mobile":
        return "/shop/pubg-mobile-uc"
      case "Call of Duty":
        return "/shop/cod-mobile-cp"
      default:
        return "/shop" // Fallback to shop page
    }
  }
  return (
    <>
      {/* Section Title */}
      <section className="text-center space-y-6">
        <h3 className="text-yellow-400 font-bold text-xl lg:text-3xl leading-relaxed">
          ⚡ أفضل وأرخص موقع لشحن الألعاب في السوق
        </h3>
        <div className="w-full h-px bg-gradient-to-r from-transparent via-yellow-400 to-transparent"></div>
      </section>

      {/* Game Cards Grid */}
      <section className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
        {gameCards.map((card, idx) => (
          <div
            key={idx}
            onClick={() => router.push(getProductRoute(card.title))}
            className={`relative rounded-3xl overflow-hidden h-40 lg:h-48 bg-gradient-to-br ${card.gradient} shadow-2xl hover:scale-105 transition-all duration-300 cursor-pointer group`}
          >
            <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-300" />
            <div className="relative h-full p-5 lg:p-6 flex flex-col justify-between">
              <div className="flex justify-center">
                <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-3 lg:p-4 group-hover:scale-110 transition-transform duration-300">
                  <div className="text-2xl lg:text-3xl">{card.icon}</div>
                </div>
              </div>
              <div className="text-center">
                <h4 className="text-white font-bold text-sm lg:text-base mb-1 drop-shadow-lg">{card.title}</h4>
                <p className="text-white/90 text-xs lg:text-sm drop-shadow-md">{card.subtitle}</p>
              </div>
            </div>
            {/* Shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 transform translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
          </div>
        ))}
      </section>
    </>
  )
}
