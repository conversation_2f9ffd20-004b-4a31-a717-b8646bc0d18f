"use client"

import { useEffect, useState } from "react"
import { ChatButton } from "./AdminChatButton"
import { ChatModal } from "./AdminChatModal"

interface GlobalChatEventHandlerProps {
  userRole: "admin" | "customer" | "guest"
  userId?: string
  userName?: string
  userEmail?: string
}

/**
 * Global Chat Event Handler
 * 
 * This component handles global chat events and ensures the chat system
 * is accessible from anywhere in the application.
 * 
 * It listens for custom events to open the chat modal with specific context
 * (like order ID or customer ID) and provides a consistent chat experience.
 */
export function GlobalChatEventHandler({
  userRole,
  userId = "",
  userName = "",
  userEmail = ""
}: GlobalChatEventHandlerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [chatContext, setChatContext] = useState<{
    customerId?: string
    orderId?: string
    customerName?: string
    customerEmail?: string
    packageName?: string
    unreadCount?: number
    isOnline?: boolean
  }>({})

  // Listen for global chat events
  useEffect(() => {
    const handleOpenChat = (event: CustomEvent) => {
      const detail = event.detail || {}
      
      setChatContext({
        customerId: detail.customerId || "",
        orderId: detail.orderId || "",
        customerName: detail.customerName || "",
        customerEmail: detail.customerEmail || "",
        packageName: detail.packageName || "",
        unreadCount: detail.unreadCount || 0,
        isOnline: detail.isOnline || false
      })
      
      setIsModalOpen(true)
    }

    // Add event listener for custom event
    window.addEventListener('open-admin-chat' as any, handleOpenChat as any)
    
    // Cleanup
    return () => {
      window.removeEventListener('open-admin-chat' as any, handleOpenChat as any)
    }
  }, [])

  // Only render for admin users
  if (userRole !== "admin") return null

  return (
    <>
      {/* Hidden trigger button for other components to use */}
      <button 
        id="admin-chat-modal-trigger"
        className="hidden"
        onClick={() => setIsModalOpen(true)}
      />
      
      {/* Direct Chat Modal - No Background Wrapper */}
      {isModalOpen && (
        <ChatModal
          isOpen={true}
          onClose={() => setIsModalOpen(false)}
          userId={userId || "admin_current"}
          userName={userName || "مدير النظام"}
          userEmail={userEmail || "<EMAIL>"}
          userRole="admin"
          position="center"
          initialCustomerId={chatContext.customerId}
          initialOrderId={chatContext.orderId}
          initialChatRoomId={chatContext.customerId}
        />
      )}
    </>
  )
}
