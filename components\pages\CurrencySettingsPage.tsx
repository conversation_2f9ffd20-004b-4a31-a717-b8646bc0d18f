"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { 
  Settings, 
  Plus, 
  Edit, 
  Trash2, 
  Power, 
  PowerOff, 
  Save,
  AlertTriangle,
  CheckCircle,
  Loader2
} from "lucide-react"
import { 
  Currency, 
  CurrencyInfo, 
  ClientCurrencySettings,
  CurrencyManagementRequest,
  CurrencyManagementResponse
} from "@/lib/types"
import { cn } from "@/lib/utils"

export function CurrencySettingsPage() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currencies, setCurrencies] = useState<CurrencyInfo[]>([])
  const [clientSettings, setClientSettings] = useState<ClientCurrencySettings | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [editingCurrency, setEditingCurrency] = useState<string | null>(null)
  const [newCurrencyForm, setNewCurrencyForm] = useState({
    code: "",
    name: "",
    symbol: "",
    arabicName: "",
    decimalPlaces: 2,
    isRTL: false,
    isActive: true,
    sortOrder: 0
  })

  // Load currencies and client settings
  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // Load currencies
      const currenciesResponse = await fetch('/api/currencies')
      const currenciesData = await currenciesResponse.json()

      if (!currenciesData.success) {
        throw new Error(currenciesData.error || 'Failed to load currencies')
      }

      setCurrencies(currenciesData.currencies)

      // Load client settings (mock for now) - USD as primary
      setClientSettings({
        id: "default",
        primaryCurrencyCode: "USD", // USD as primary currency
        enabledCurrencies: ["USD", "SDG", "EGP"], // USD first in the list
        enableMultiCurrency: true,
        enableCurrencyConversion: true,
        enableAdvancedReporting: false,
        autoUpdateRates: true,
        rateUpdateFrequencyHours: 24,
        createdAt: new Date(),
        updatedAt: new Date()
      })

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCurrencyAction = async (action: string, currencyCode?: string, currencyData?: Partial<CurrencyInfo>) => {
    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const request: CurrencyManagementRequest = {
        action: action as any,
        currencyCode,
        currency: currencyData
      }

      const response = await fetch('/api/currencies', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request)
      })

      const data: CurrencyManagementResponse = await response.json()

      if (!data.success) {
        throw new Error(data.message || 'Operation failed')
      }

      setSuccess(data.message)
      await loadData()
      
      // Reset form if creating new currency
      if (action === 'create') {
        setNewCurrencyForm({
          code: "",
          name: "",
          symbol: "",
          arabicName: "",
          decimalPlaces: 2,
          isRTL: false,
          isActive: true,
          sortOrder: 0
        })
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Operation failed')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCreateCurrency = () => {
    handleCurrencyAction('create', undefined, newCurrencyForm)
  }

  const handleToggleCurrencyStatus = (currency: CurrencyInfo) => {
    const action = currency.isActive ? 'deactivate' : 'activate'
    handleCurrencyAction(action, currency.code)
  }

  const handleDeleteCurrency = (currency: CurrencyInfo) => {
    if (confirm(`Are you sure you want to delete ${currency.name}? This action cannot be undone.`)) {
      handleCurrencyAction('delete', currency.code)
    }
  }

  const handleClientSettingsUpdate = async () => {
    if (!clientSettings) return

    setIsSaving(true)
    setError(null)
    setSuccess(null)

    try {
      // TODO: Implement client settings update API
      setSuccess('Client settings updated successfully')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update settings')
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
        <AppHeader onMenuToggle={() => setIsMenuOpen(!isMenuOpen)} />
        <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
        <main className="lg:ml-64 pt-16">
          <div className="p-6">
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-blue-400" />
              <span className="ml-2 text-slate-400">Loading currency settings...</span>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader onMenuToggle={() => setIsMenuOpen(!isMenuOpen)} />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />
      
      <main className="lg:ml-64 pt-16">
        <div className="p-6 space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Currency Settings</h1>
              <p className="text-slate-400 mt-1">Manage currencies and exchange rate settings</p>
            </div>
            <div className="flex items-center gap-2">
              <Settings className="h-6 w-6 text-blue-400" />
            </div>
          </div>

          {/* News Ticket */}
          <NewsTicket />

          {/* Alerts */}
          {error && (
            <Alert className="bg-red-900/20 border-red-700/50">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-100">{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="bg-green-900/20 border-green-700/50">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <AlertDescription className="text-green-100">{success}</AlertDescription>
            </Alert>
          )}

          {/* Client Settings */}
          {clientSettings && (
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-xl text-white">Client Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="text-slate-300">Primary Currency</Label>
                    <Select 
                      value={clientSettings.primaryCurrencyCode} 
                      onValueChange={(value) => setClientSettings({
                        ...clientSettings,
                        primaryCurrencyCode: value as Currency
                      })}
                    >
                      <SelectTrigger className="bg-slate-700/50 border-slate-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {currencies.filter(c => c.isActive).map((currency) => (
                          <SelectItem key={currency.code} value={currency.code}>
                            {currency.symbol} {currency.code} - {currency.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Rate Update Frequency (hours)</Label>
                    <Input
                      type="number"
                      value={clientSettings.rateUpdateFrequencyHours}
                      onChange={(e) => setClientSettings({
                        ...clientSettings,
                        rateUpdateFrequencyHours: parseInt(e.target.value) || 24
                      })}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      min="1"
                      max="168"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <Label className="text-slate-300">Multi-Currency</Label>
                    <Switch
                      checked={clientSettings.enableMultiCurrency}
                      onCheckedChange={(checked) => setClientSettings({
                        ...clientSettings,
                        enableMultiCurrency: checked
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <Label className="text-slate-300">Currency Conversion</Label>
                    <Switch
                      checked={clientSettings.enableCurrencyConversion}
                      onCheckedChange={(checked) => setClientSettings({
                        ...clientSettings,
                        enableCurrencyConversion: checked
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <Label className="text-slate-300">Advanced Reporting</Label>
                    <Switch
                      checked={clientSettings.enableAdvancedReporting}
                      onCheckedChange={(checked) => setClientSettings({
                        ...clientSettings,
                        enableAdvancedReporting: checked
                      })}
                    />
                  </div>

                  <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                    <Label className="text-slate-300">Auto Update Rates</Label>
                    <Switch
                      checked={clientSettings.autoUpdateRates}
                      onCheckedChange={(checked) => setClientSettings({
                        ...clientSettings,
                        autoUpdateRates: checked
                      })}
                    />
                  </div>
                </div>

                <Button
                  onClick={handleClientSettingsUpdate}
                  disabled={isSaving}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Currency Management */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Add New Currency */}
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <Plus className="h-5 w-5 text-green-400" />
                  Add New Currency
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-slate-300">Currency Code</Label>
                    <Input
                      placeholder="USD"
                      value={newCurrencyForm.code}
                      onChange={(e) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        code: e.target.value.toUpperCase()
                      })}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      maxLength={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Symbol</Label>
                    <Input
                      placeholder="$"
                      value={newCurrencyForm.symbol}
                      onChange={(e) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        symbol: e.target.value
                      })}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-slate-300">Currency Name</Label>
                  <Input
                    placeholder="US Dollar"
                    value={newCurrencyForm.name}
                    onChange={(e) => setNewCurrencyForm({
                      ...newCurrencyForm,
                      name: e.target.value
                    })}
                    className="bg-slate-700/50 border-slate-600 text-white"
                  />
                </div>



                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-slate-300">Decimal Places</Label>
                    <Input
                      type="number"
                      value={newCurrencyForm.decimalPlaces}
                      onChange={(e) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        decimalPlaces: parseInt(e.target.value) || 2
                      })}
                      className="bg-slate-700/50 border-slate-600 text-white"
                      min="0"
                      max="8"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-slate-300">Sort Order</Label>
                    <Input
                      type="number"
                      value={newCurrencyForm.sortOrder}
                      onChange={(e) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        sortOrder: parseInt(e.target.value) || 0
                      })}
                      className="bg-slate-700/50 border-slate-600 text-white"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={newCurrencyForm.isRTL}
                      onCheckedChange={(checked) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        isRTL: checked
                      })}
                    />
                    <Label className="text-slate-300">Right-to-Left</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={newCurrencyForm.isActive}
                      onCheckedChange={(checked) => setNewCurrencyForm({
                        ...newCurrencyForm,
                        isActive: checked
                      })}
                    />
                    <Label className="text-slate-300">Active</Label>
                  </div>
                </div>

                <Button
                  onClick={handleCreateCurrency}
                  disabled={!newCurrencyForm.code || !newCurrencyForm.name || !newCurrencyForm.symbol || isSaving}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Currency
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Existing Currencies */}
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-xl text-white">Existing Currencies</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {currencies.map((currency) => (
                    <div
                      key={currency.id}
                      className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div className="text-lg font-bold text-white">
                          {currency.symbol}
                        </div>
                        <div>
                          <div className="font-medium text-white">{currency.code}</div>
                          <div className="text-sm text-slate-400">{currency.name}</div>
                          {currency.arabicName && (
                            <div className="text-xs text-slate-500">{currency.arabicName}</div>
                          )}
                        </div>
                        <div className="flex gap-1">
                          <Badge variant={currency.isActive ? "default" : "secondary"}>
                            {currency.isActive ? "Active" : "Inactive"}
                          </Badge>
                          {currency.isRTL && (
                            <Badge variant="outline">RTL</Badge>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleToggleCurrencyStatus(currency)}
                          disabled={isSaving}
                          className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
                        >
                          {currency.isActive ? (
                            <PowerOff className="h-4 w-4" />
                          ) : (
                            <Power className="h-4 w-4" />
                          )}
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setEditingCurrency(currency.id)}
                          disabled={isSaving}
                          className="bg-slate-600/50 border-slate-500 text-white hover:bg-slate-500/50"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteCurrency(currency)}
                          disabled={isSaving}
                          className="bg-red-600/50 border-red-500 text-white hover:bg-red-500/50"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  {currencies.length === 0 && (
                    <div className="text-center py-8 text-slate-400">
                      No currencies configured yet
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  )
}
