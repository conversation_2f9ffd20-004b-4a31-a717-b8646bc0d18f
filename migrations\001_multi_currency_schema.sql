-- =====================================================
-- Al-Raya Store Multi-Currency Platform Migration
-- Phase 1: Core Schema Creation
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- 1. CURRENCIES TABLE
-- =====================================================
CREATE TABLE currencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(3) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  decimal_places INTEGER DEFAULT 2 CHECK (decimal_places >= 0 AND decimal_places <= 8),
  is_rtl BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_currencies_code ON currencies(code);
CREATE INDEX idx_currencies_active ON currencies(is_active);
CREATE INDEX idx_currencies_sort ON currencies(sort_order);

-- =====================================================
-- 2. EXCHANGE RATES TABLE
-- =====================================================
CREATE TABLE exchange_rates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  from_currency_code VARCHAR(3) NOT NULL,
  to_currency_code VARCHAR(3) NOT NULL,
  rate DECIMAL(18,8) NOT NULL CHECK (rate > 0),
  effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  
  -- Ensure we have foreign key relationships
  FOREIGN KEY (from_currency_code) REFERENCES currencies(code) ON DELETE CASCADE,
  FOREIGN KEY (to_currency_code) REFERENCES currencies(code) ON DELETE CASCADE,
  
  -- Prevent duplicate active rates for same currency pair
  UNIQUE(from_currency_code, to_currency_code, effective_date)
);

-- Create indexes for performance
CREATE INDEX idx_exchange_rates_from_to ON exchange_rates(from_currency_code, to_currency_code);
CREATE INDEX idx_exchange_rates_effective_date ON exchange_rates(effective_date DESC);
CREATE INDEX idx_exchange_rates_active ON exchange_rates(is_active);

-- =====================================================
-- 3. CLIENT CURRENCY SETTINGS TABLE
-- =====================================================
CREATE TABLE client_currency_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID, -- For multi-tenant support (can be NULL for single tenant)
  primary_currency_code VARCHAR(3) NOT NULL,
  enabled_currencies TEXT[] DEFAULT '{}',
  enable_multi_currency BOOLEAN DEFAULT true,
  enable_currency_conversion BOOLEAN DEFAULT true,
  enable_advanced_reporting BOOLEAN DEFAULT false,
  auto_update_rates BOOLEAN DEFAULT true,
  rate_update_frequency_hours INTEGER DEFAULT 24,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  FOREIGN KEY (primary_currency_code) REFERENCES currencies(code)
);

-- Create indexes
CREATE INDEX idx_client_currency_settings_client ON client_currency_settings(client_id);
CREATE INDEX idx_client_currency_settings_primary ON client_currency_settings(primary_currency_code);

-- =====================================================
-- 4. CURRENCY AUDIT LOG TABLE
-- =====================================================
CREATE TABLE currency_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  table_name VARCHAR(50) NOT NULL,
  record_id UUID NOT NULL,
  action VARCHAR(20) NOT NULL CHECK (action IN ('INSERT', 'UPDATE', 'DELETE')),
  old_values JSONB,
  new_values JSONB,
  changed_by UUID REFERENCES auth.users(id),
  changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

-- Create indexes for audit log
CREATE INDEX idx_currency_audit_log_table_record ON currency_audit_log(table_name, record_id);
CREATE INDEX idx_currency_audit_log_changed_at ON currency_audit_log(changed_at DESC);
CREATE INDEX idx_currency_audit_log_changed_by ON currency_audit_log(changed_by);

-- =====================================================
-- 5. ENHANCED USER WALLETS TABLE
-- =====================================================
-- First, let's create the new enhanced user_wallets table
CREATE TABLE user_wallets_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  currency_code VARCHAR(3) NOT NULL,
  balance DECIMAL(18,8) DEFAULT 0.00 CHECK (balance >= 0),
  reserved_balance DECIMAL(18,8) DEFAULT 0.00 CHECK (reserved_balance >= 0),
  total_deposits DECIMAL(18,8) DEFAULT 0.00,
  total_withdrawals DECIMAL(18,8) DEFAULT 0.00,
  total_purchases DECIMAL(18,8) DEFAULT 0.00,
  last_transaction_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  FOREIGN KEY (currency_code) REFERENCES currencies(code),
  UNIQUE(user_id, currency_code)
);

-- Create indexes for performance
CREATE INDEX idx_user_wallets_new_user_currency ON user_wallets_new(user_id, currency_code);
CREATE INDEX idx_user_wallets_new_currency ON user_wallets_new(currency_code);
CREATE INDEX idx_user_wallets_new_updated ON user_wallets_new(updated_at DESC);

-- =====================================================
-- 6. ENHANCED WALLET TRANSACTIONS TABLE
-- =====================================================
CREATE TABLE wallet_transactions_new (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  wallet_id UUID REFERENCES user_wallets_new(id) ON DELETE CASCADE,
  transaction_type VARCHAR(20) NOT NULL CHECK (
    transaction_type IN ('deposit', 'withdrawal', 'purchase', 'refund', 'currency_conversion', 'admin_adjustment')
  ),
  amount DECIMAL(18,8) NOT NULL,
  currency_code VARCHAR(3) NOT NULL,
  
  -- For currency conversions
  original_amount DECIMAL(18,8),
  original_currency_code VARCHAR(3),
  exchange_rate DECIMAL(18,8),
  conversion_fee DECIMAL(18,8) DEFAULT 0.00,
  
  -- Transaction details
  description TEXT NOT NULL,
  reference_number VARCHAR(100),
  external_reference VARCHAR(100),
  
  -- Status and metadata
  status VARCHAR(20) DEFAULT 'completed' CHECK (
    status IN ('pending', 'completed', 'failed', 'cancelled', 'refunded')
  ),
  metadata JSONB DEFAULT '{}',
  
  -- Related records
  order_id VARCHAR(50),
  related_transaction_id UUID REFERENCES wallet_transactions_new(id),
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES auth.users(id),
  
  FOREIGN KEY (currency_code) REFERENCES currencies(code),
  FOREIGN KEY (original_currency_code) REFERENCES currencies(code)
);

-- Create indexes for wallet transactions
CREATE INDEX idx_wallet_transactions_new_user ON wallet_transactions_new(user_id);
CREATE INDEX idx_wallet_transactions_new_wallet ON wallet_transactions_new(wallet_id);
CREATE INDEX idx_wallet_transactions_new_type ON wallet_transactions_new(transaction_type);
CREATE INDEX idx_wallet_transactions_new_status ON wallet_transactions_new(status);
CREATE INDEX idx_wallet_transactions_new_currency ON wallet_transactions_new(currency_code);
CREATE INDEX idx_wallet_transactions_new_created ON wallet_transactions_new(created_at DESC);
CREATE INDEX idx_wallet_transactions_new_order ON wallet_transactions_new(order_id);

-- =====================================================
-- 7. ENHANCED PRODUCT ORDERS TABLE
-- =====================================================
CREATE TABLE product_orders_new (
  id VARCHAR(50) PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id),
  template_id VARCHAR(100) NOT NULL,
  template_name VARCHAR(200) NOT NULL,
  template_category VARCHAR(100),
  
  -- Product and pricing data
  product_data JSONB NOT NULL DEFAULT '{}',
  pricing_data JSONB NOT NULL DEFAULT '{}',
  
  -- Currency and exchange rate snapshot
  currency_code VARCHAR(3) NOT NULL,
  base_price_usd DECIMAL(18,8),
  exchange_rate_snapshot DECIMAL(18,8),
  total_price DECIMAL(18,8) NOT NULL,
  
  -- Customer details
  customer_name VARCHAR(200),
  customer_email VARCHAR(200),
  customer_phone VARCHAR(50),
  
  -- Order status and processing
  status VARCHAR(20) DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')
  ),
  processing_type VARCHAR(20) DEFAULT 'manual' CHECK (
    processing_type IN ('auto', 'manual', 'review')
  ),
  
  -- Timeline and metadata
  timeline_events JSONB DEFAULT '[]',
  metadata JSONB DEFAULT '{}',
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  
  FOREIGN KEY (currency_code) REFERENCES currencies(code)
);

-- Create indexes for product orders
CREATE INDEX idx_product_orders_new_user ON product_orders_new(user_id);
CREATE INDEX idx_product_orders_new_status ON product_orders_new(status);
CREATE INDEX idx_product_orders_new_currency ON product_orders_new(currency_code);
CREATE INDEX idx_product_orders_new_created ON product_orders_new(created_at DESC);
CREATE INDEX idx_product_orders_new_template ON product_orders_new(template_id);

-- =====================================================
-- 8. USER PREFERENCES TABLE
-- =====================================================
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  preferred_currency_code VARCHAR(3),
  display_currency_code VARCHAR(3),
  enable_currency_conversion BOOLEAN DEFAULT true,
  conversion_confirmation_required BOOLEAN DEFAULT true,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  FOREIGN KEY (preferred_currency_code) REFERENCES currencies(code),
  FOREIGN KEY (display_currency_code) REFERENCES currencies(code)
);

-- Create indexes for user preferences
CREATE INDEX idx_user_preferences_user ON user_preferences(user_id);
CREATE INDEX idx_user_preferences_preferred_currency ON user_preferences(preferred_currency_code);
