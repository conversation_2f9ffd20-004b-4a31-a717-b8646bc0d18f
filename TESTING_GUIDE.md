# دليل الاختبار | Testing Guide

## 🧪 اختبار النظام الكامل | Complete System Testing

### 1. اختبار لوحة الإدارة | Admin Dashboard Testing

#### الوصول للوحة الإدارة
```bash
# تشغيل التطبيق
npm run dev

# فتح لوحة الإدارة
http://localhost:3000/admin
```

#### اختبار إنشاء المنتجات
1. **النقر على تبويب "المنتجات"**
2. **النقر على "إضافة منتج جديد"**
3. **ملء المعلومات الأساسية**:
   - اسم المنتج: "شحن يوسي PUBG Mobile"
   - الوصف: "شحن فوري لعملة UC"
   - الفئة: "ألعاب الموبايل"
   - نوع المنتج: "رقمي"
   - نوع المعالجة: "فوري"

4. **إضافة الحزم**:
   - حزمة 60 UC - $5
   - حزمة 325 UC - $25 (شائعة)
   - حزمة 660 UC - $50

5. **إضافة الحقول المخصصة**:
   - معرف اللاعب (نص، مطلوب)
   - الخادم (قائمة منسدلة)

6. **حفظ المنتج**

#### النتائج المتوقعة ✅
- [x] المنتج يظهر في قائمة المنتجات
- [x] الإحصائيات تتحدث تلقائياً
- [x] يمكن تعديل المنتج
- [x] يمكن حذف المنتج

### 2. اختبار المتجر | Shop Testing

#### تصفح المنتجات
```bash
# فتح صفحة المتجر
http://localhost:3000/shop
```

#### اختبار عرض المنتجات
1. **التحقق من عرض المنتجات المنشأة**
2. **النقر على منتج للدخول لصفحته**
3. **اختبار اختيار الحزم**
4. **ملء الحقول المخصصة**
5. **اختبار حساب السعر**
6. **محاكاة عملية الشراء**

#### النتائج المتوقعة ✅
- [x] المنتجات تظهر بالتصميم العربي
- [x] الحزم تظهر مع الأسعار الصحيحة
- [x] الحقول المخصصة تعمل بشكل صحيح
- [x] التحقق من صحة البيانات يعمل
- [x] حساب السعر الإجمالي صحيح

### 3. اختبار المحفظة | Wallet Testing

#### عرض الطلبات
```bash
# فتح صفحة المحفظة
http://localhost:3000/wallet
```

#### اختبار المحتوى الرقمي
1. **التحقق من ظهور الطلبات**
2. **النقر على طلب مكتمل**
3. **اختبار عرض الأكواد الرقمية**
4. **اختبار إخفاء/إظهار الأكواد**
5. **اختبار نسخ الأكواد**

#### النتائج المتوقعة ✅
- [x] الطلبات تظهر مع الحالة الصحيحة
- [x] الأكواد الرقمية محمية افتراضياً
- [x] يمكن إظهار الأكواد بالنقر على العين
- [x] يمكن نسخ الأكواد بنجاح
- [x] التعليمات تظهر بوضوح

### 4. اختبار التصميم المتجاوب | Responsive Design Testing

#### اختبار الهواتف المحمولة
```bash
# فتح أدوات المطور
F12 → Device Toolbar

# اختبار الأحجام:
- iPhone SE (375px)
- iPhone 12 Pro (390px)
- iPad (768px)
- Desktop (1024px+)
```

#### النقاط المهمة للاختبار
- [x] الأزرار بحجم 44px للمس
- [x] النصوص قابلة للقراءة
- [x] التنقل سهل بإصبع واحد
- [x] النماذج تعمل على الشاشات الصغيرة
- [x] الجداول تتكيف مع الشاشة

### 5. اختبار اللغة العربية | Arabic Language Testing

#### اختبار RTL
- [x] النصوص تظهر من اليمين لليسار
- [x] الأيقونات في المواضع الصحيحة
- [x] القوائم المنسدلة تفتح بالاتجاه الصحيح
- [x] النماذج منسقة بشكل صحيح

#### اختبار المحتوى العربي
- [x] الخطوط العربية واضحة
- [x] المسافات بين الكلمات صحيحة
- [x] الأرقام تظهر بالشكل الصحيح
- [x] التواريخ بالتنسيق العربي

### 6. اختبار الأداء | Performance Testing

#### سرعة التحميل
```bash
# اختبار سرعة الصفحات
npm run build
npm run start

# قياس الأداء في أدوات المطور
Lighthouse → Performance
```

#### النتائج المستهدفة
- [x] First Contentful Paint < 2s
- [x] Largest Contentful Paint < 4s
- [x] Cumulative Layout Shift < 0.1
- [x] First Input Delay < 100ms

### 7. اختبار البيانات | Data Testing

#### البيانات الافتراضية
النظام يأتي مع 3 منتجات تجريبية:

1. **PUBG Mobile UC**
   - 4 حزم مختلفة
   - حقلين مخصصين
   - منتج رقمي فوري

2. **Free Fire Diamonds**
   - 3 حزم مختلفة
   - حقل واحد مخصص
   - منتج رقمي فوري

3. **Google Play Gift Card**
   - 3 حزم مختلفة
   - حقل بريد إلكتروني
   - منتج رقمي فوري

#### اختبار البيانات
- [x] المنتجات تحمل بشكل صحيح
- [x] الحزم تظهر مع الأسعار
- [x] الحقول المخصصة تعمل
- [x] البيانات تحفظ في localStorage

### 8. اختبار الأخطاء | Error Testing

#### اختبار التحقق من البيانات
1. **ترك الحقول المطلوبة فارغة**
2. **إدخال بيانات غير صحيحة**
3. **اختبار الحد الأدنى والأقصى للأرقام**
4. **اختبار تنسيق البريد الإلكتروني**

#### النتائج المتوقعة
- [x] رسائل خطأ واضحة بالعربية
- [x] منع إرسال النماذج الناقصة
- [x] تمييز الحقول الخاطئة بالأحمر
- [x] إرشادات لتصحيح الأخطاء

### 9. اختبار التكامل | Integration Testing

#### نقاط التكامل المستقبلية
جميع النقاط محددة بوضوح في الكود:

```typescript
// ## TODO: تنفيذ مع Supabase
// ## DATABASE LATER: ربط بقاعدة البيانات
```

#### الملفات المهمة للتكامل
- `lib/services/productService.ts`
- `lib/services/digitalContentService.ts`
- `migrations/006_product_management_schema.sql`

### 10. قائمة التحقق النهائية | Final Checklist

#### الوظائف الأساسية ✅
- [x] إنشاء المنتجات
- [x] تعديل المنتجات
- [x] حذف المنتجات
- [x] عرض المنتجات في المتجر
- [x] نماذج الشراء التفاعلية
- [x] عرض الطلبات في المحفظة
- [x] المحتوى الرقمي

#### التصميم والتجربة ✅
- [x] تصميم عربي أولاً
- [x] متجاوب مع جميع الشاشات
- [x] سهولة الاستخدام
- [x] ألوان وأيقونات واضحة
- [x] تنقل سلس

#### الأمان والجودة ✅
- [x] التحقق من البيانات
- [x] حماية الأكواد الرقمية
- [x] رسائل خطأ واضحة
- [x] كود نظيف ومنظم
- [x] تعليقات شاملة للتكامل

## 🚀 الخطوات التالية | Next Steps

### للتطوير
1. **تنفيذ تكامل Supabase** - استخدام INTEGRATION_GUIDE.md
2. **إضافة نظام الدفع** - تكامل مع بوابات الدفع
3. **تحسين الأداء** - تحسين الصور والتخزين المؤقت
4. **إضافة الإشعارات** - إشعارات فورية للطلبات

### للنشر
1. **إعداد متغيرات البيئة** - Supabase والمفاتيح
2. **تشغيل الاختبارات** - جميع الاختبارات المذكورة أعلاه
3. **نشر قاعدة البيانات** - تشغيل ملفات المخطط
4. **مراقبة الأداء** - إعداد التحليلات والمراقبة

---

**✅ النظام جاهز للاستخدام مع جميع الوظائف الأساسية**

**🔗 جاهز للتكامل مع Supabase والنشر في الإنتاج**
